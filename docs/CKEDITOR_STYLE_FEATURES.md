# CKEditor-Style Block Content Features

This document outlines the comprehensive CKEditor-style capabilities added to the `blockContentType` schema in Sanity.

## Overview

The enhanced `blockContentType` now provides a rich text editing experience similar to CKEditor, with advanced formatting options, media embeds, and custom components.

## Text Formatting Features

### Heading Styles

- **Normal Text** - Standard paragraph text
- **Heading 1-6** - Complete hierarchy of headings (h1-h6)
- **Quote** - Blockquote styling
- **Code Block** - Preformatted code text
- **Lead Text** - Emphasized introductory text
- **Small Text** - Fine print or secondary text

### Inline Formatting (Decorators)

- **Bold** (`strong`) - Bold text emphasis
- **Italic** (`em`) - Italic text emphasis
- **Underline** - Underlined text
- **Strike-through** - Crossed-out text
- **Code** - Inline code formatting
- **Highlight** - Highlighted/marked text
- **Superscript** - Raised text (e.g., footnotes)
- **Subscript** - Lowered text (e.g., chemical formulas)
- **Large Text** - Larger font size
- **Small Text** - Smaller font size
- **Extra Large** - Extra large font size
- **Extra Small** - Extra small font size

### List Types

- **Bullet List** - Unordered lists with bullets
- **Numbered List** - Ordered lists with numbers
- **Checklist** - Interactive checkbox lists

## Advanced Link Features

### External Links

- **URL** - Required link destination
- **Link Text** - Custom link text override
- **Open in New Tab** - Target="\_blank" option
- **No Follow** - SEO rel="nofollow" attribute

### Internal Links

- **Reference** - Link to other documents (posts, pages)
- **Link Text** - Custom text for internal links

### Footnotes

- **Footnote Text** - Rich footnote content
- Automatic footnote numbering and linking

## Typography & Font Features

### Font Families

- **Default** - System default font
- **Serif** - Times New Roman style fonts
- **Sans Serif** - Arial style fonts
- **Monospace** - Courier style fonts
- **Cursive** - Script/handwriting fonts
- **Fantasy** - Decorative fonts
- **Modern Web Fonts**:
  - Inter, Roboto, Open Sans
  - Lato, Montserrat, Poppins
  - Playfair Display, Merriweather

### Font Sizes

- **Extra Small** - 12px
- **Small** - 14px
- **Base** - 16px (default)
- **Large** - 18px
- **Extra Large** - 20px
- **2X Large** - 24px
- **3X Large** - 30px
- **4X Large** - 36px

### Font Weights

- **Thin** - 100
- **Light** - 300
- **Normal** - 400 (default)
- **Medium** - 500
- **Semibold** - 600
- **Bold** - 700
- **Extra Bold** - 800
- **Black** - 900

### Text Colors

- **Theme Colors**: Primary, Secondary, Accent, Muted
- **Status Colors**: Success, Warning, Error, Info
- **Standard Colors**: Black, White, Gray
- **Vibrant Colors**: Red, Blue, Green, Yellow, Purple, Pink, Indigo

### Background Colors

- **None** - No background
- **Light Variants**: Gray, Blue, Green, Yellow, Red, Purple
- **Theme Variants**: Primary Light, Secondary Light

### Text Styling Options

- **Text Transform**: None, Uppercase, Lowercase, Capitalize
- **Letter Spacing**: Normal, Tight, Wide, Wider, Widest
- **Text Alignment**: Left, Center, Right, Justify

## Media Components

### Enhanced Images

- **Alternative Text** - Required for accessibility
- **Caption** - Optional image caption
- **Attribution** - Photo credit or source
- **Size Options**:
  - Small
  - Medium
  - Large
  - Full Width
- **Alignment Options**:
  - Left
  - Center
  - Right
- **Hotspot Support** - Focus point selection

### Video Embeds

- **Video URL** - YouTube, Vimeo, or direct URLs
- **Video Title** - Accessible title
- **Aspect Ratio Options**:
  - 16:9 (Widescreen)
  - 4:3 (Standard)
  - 1:1 (Square)
  - 9:16 (Vertical)

## Code Features

### Code Blocks

- **Language Selection**:
  - JavaScript, TypeScript
  - HTML, CSS
  - Python, Java, C++
  - JSON, Markdown
  - Shell, SQL
  - PHP, Ruby, Go, Rust
  - Plain Text
- **Filename Display** - Optional file header
- **Line Highlighting** - Specify lines to emphasize
- **Syntax Highlighting** - Language-specific formatting

## Content Components

### Callouts/Alerts

- **Types Available**:
  - Info (blue)
  - Warning (yellow)
  - Error (red)
  - Success (green)
  - Note (gray)
  - Tip (purple)
- **Optional Title** - Custom callout header
- **Rich Content** - Full formatting within callouts

### Tables

- **Caption** - Optional table description
- **Dynamic Rows/Columns** - Add/remove as needed
- **Header Row Option** - First row as table header
- **Cell Content** - Text content in each cell

### Dividers

- **Style Options**:
  - Line - Simple horizontal line
  - Dots - Dotted separator
  - Stars - Decorative stars
  - Spacing - Invisible spacing
- **Size Options**:
  - Small, Medium, Large

## Usage Examples

### In Sanity Schema

```typescript
{
  name: 'content',
  title: 'Article Content',
  type: 'blockContent'
}
```

### In Frontend (React/Astro)

```typescript
import { PortableText } from '@portabletext/react'

// Custom components for enhanced blocks
const components = {
  types: {
    codeBlock: CodeBlockComponent,
    callout: CalloutComponent,
    table: TableComponent,
    videoEmbed: VideoEmbedComponent,
    divider: DividerComponent,
  },
  marks: {
    // Typography marks
    highlight: ({ children }) => <mark>{children}</mark>,
    underline: ({ children }) => <u>{children}</u>,
    'strike-through': ({ children }) => <s>{children}</s>,
    large: ({ children }) => <span className="text-lg">{children}</span>,
    small: ({ children }) => <span className="text-sm">{children}</span>,
    xl: ({ children }) => <span className="text-xl">{children}</span>,
    xs: ({ children }) => <span className="text-xs">{children}</span>,

    // Font styling annotation
    fontStyle: ({ children, value }) => {
      const {
        fontFamily,
        fontSize,
        fontWeight,
        color,
        backgroundColor,
        textTransform,
        letterSpacing
      } = value;

      const className = [
        fontFamily && `font-${fontFamily}`,
        fontSize && `text-${fontSize}`,
        fontWeight && `font-${fontWeight}`,
        color && `text-${color}`,
        backgroundColor && backgroundColor !== 'none' && `bg-${backgroundColor}`,
        textTransform && textTransform !== 'none' && `${textTransform}`,
        letterSpacing && letterSpacing !== 'normal' && `tracking-${letterSpacing}`,
      ].filter(Boolean).join(' ');

      return <span className={className}>{children}</span>;
    },

    // Text alignment annotation
    textAlign: ({ children, value }) => {
      const { align } = value;
      return <span className={`text-${align}`}>{children}</span>;
    },
  },
  block: {
    h1: ({ children }) => <h1 className="text-4xl font-bold">{children}</h1>,
    h2: ({ children }) => <h2 className="text-3xl font-bold">{children}</h2>,
    h3: ({ children }) => <h3 className="text-2xl font-bold">{children}</h3>,
    h4: ({ children }) => <h4 className="text-xl font-bold">{children}</h4>,
    h5: ({ children }) => <h5 className="text-lg font-bold">{children}</h5>,
    h6: ({ children }) => <h6 className="text-base font-bold">{children}</h6>,
    lead: ({ children }) => <p className="text-xl text-gray-600 leading-relaxed">{children}</p>,
    small: ({ children }) => <small className="text-sm text-gray-500">{children}</small>,
    code: ({ children }) => <pre className="bg-gray-100 p-4 rounded font-mono text-sm overflow-x-auto">{children}</pre>,
  },
}

<PortableText value={content} components={components} />
```

### CSS Font Classes (Tailwind Example)

```css
/* Font Families */
.font-default {
  font-family:
    system-ui,
    -apple-system,
    sans-serif;
}
.font-serif {
  font-family: Georgia, serif;
}
.font-sans-serif {
  font-family: Arial, sans-serif;
}
.font-monospace {
  font-family: 'Courier New', monospace;
}
.font-inter {
  font-family: 'Inter', sans-serif;
}
.font-roboto {
  font-family: 'Roboto', sans-serif;
}
.font-playfair {
  font-family: 'Playfair Display', serif;
}

/* Custom Colors */
.text-primary {
  color: var(--color-primary);
}
.text-secondary {
  color: var(--color-secondary);
}
.text-accent {
  color: var(--color-accent);
}
.text-muted {
  color: var(--color-muted);
}

/* Background Colors */
.bg-primary-light {
  background-color: var(--color-primary-light);
}
.bg-gray-light {
  background-color: #f8f9fa;
}
.bg-blue-light {
  background-color: #e3f2fd;
}
.bg-green-light {
  background-color: #e8f5e8;
}

/* Letter Spacing */
.tracking-tight {
  letter-spacing: -0.025em;
}
.tracking-wide {
  letter-spacing: 0.025em;
}
.tracking-wider {
  letter-spacing: 0.05em;
}
.tracking-widest {
  letter-spacing: 0.1em;
}
```

## Benefits

### For Content Creators

- **Familiar Interface** - Similar to popular editors like CKEditor
- **Rich Formatting** - Comprehensive text styling options
- **Media Integration** - Easy image and video embedding
- **Structured Content** - Tables, callouts, and code blocks
- **Accessibility** - Built-in alt text and semantic markup

### For Developers

- **Type Safety** - Full TypeScript support
- **Flexible Rendering** - Custom component mapping
- **SEO Friendly** - Semantic HTML output
- **Performance** - Optimized portable text rendering
- **Extensible** - Easy to add new component types

### For Users

- **Better Reading Experience** - Rich, formatted content
- **Interactive Elements** - Checklists, video embeds
- **Code Examples** - Syntax-highlighted code blocks
- **Visual Hierarchy** - Clear content structure

## Customization

### Adding New Components

```typescript
// Add to blockContent.ts
defineArrayMember({
  type: 'object',
  name: 'customComponent',
  title: 'Custom Component',
  fields: [
    // Define your fields
  ],
});
```

### Custom Decorators

```typescript
// Add to marks.decorators
{ title: "Custom Style", value: "customStyle" }
```

### New List Types

```typescript
// Add to lists array
{ title: "Custom List", value: "customList" }
```

## Best Practices

1. **Use Semantic Markup** - Choose appropriate heading levels
2. **Alt Text Required** - Always provide image descriptions
3. **Link Accessibility** - Use descriptive link text
4. **Code Documentation** - Include language and filename
5. **Table Headers** - Use header rows for data tables
6. **Callout Moderation** - Don't overuse attention-grabbing elements

## Migration from Basic Block Content

Existing content will continue to work. New features are additive:

- Old text formatting remains unchanged
- New decorators and components are optional
- Gradual adoption of enhanced features
- Backward compatibility maintained
