# CKEditor-Style Block Content Features

This document outlines the comprehensive CKEditor-style capabilities added to the `blockContentType` schema in Sanity.

## Overview

The enhanced `blockContentType` now provides a rich text editing experience similar to CKEditor, with advanced formatting options, media embeds, and custom components.

## Text Formatting Features

### Heading Styles
- **Normal Text** - Standard paragraph text
- **Heading 1-6** - Complete hierarchy of headings (h1-h6)
- **Quote** - Blockquote styling
- **Code Block** - Preformatted code text
- **Lead Text** - Emphasized introductory text
- **Small Text** - Fine print or secondary text

### Inline Formatting (Decorators)
- **Bold** (`strong`) - Bold text emphasis
- **Italic** (`em`) - Italic text emphasis
- **Underline** - Underlined text
- **Strike-through** - Crossed-out text
- **Code** - Inline code formatting
- **Highlight** - Highlighted/marked text
- **Superscript** - Raised text (e.g., footnotes)
- **Subscript** - Lowered text (e.g., chemical formulas)

### List Types
- **Bullet List** - Unordered lists with bullets
- **Numbered List** - Ordered lists with numbers
- **Checklist** - Interactive checkbox lists

## Advanced Link Features

### External Links
- **URL** - Required link destination
- **Link Text** - Custom link text override
- **Open in New Tab** - Target="_blank" option
- **No Follow** - SEO rel="nofollow" attribute

### Internal Links
- **Reference** - Link to other documents (posts, pages)
- **Link Text** - Custom text for internal links

### Footnotes
- **Footnote Text** - Rich footnote content
- Automatic footnote numbering and linking

## Media Components

### Enhanced Images
- **Alternative Text** - Required for accessibility
- **Caption** - Optional image caption
- **Attribution** - Photo credit or source
- **Size Options**:
  - Small
  - Medium
  - Large
  - Full Width
- **Alignment Options**:
  - Left
  - Center
  - Right
- **Hotspot Support** - Focus point selection

### Video Embeds
- **Video URL** - YouTube, Vimeo, or direct URLs
- **Video Title** - Accessible title
- **Aspect Ratio Options**:
  - 16:9 (Widescreen)
  - 4:3 (Standard)
  - 1:1 (Square)
  - 9:16 (Vertical)

## Code Features

### Code Blocks
- **Language Selection**:
  - JavaScript, TypeScript
  - HTML, CSS
  - Python, Java, C++
  - JSON, Markdown
  - Shell, SQL
  - PHP, Ruby, Go, Rust
  - Plain Text
- **Filename Display** - Optional file header
- **Line Highlighting** - Specify lines to emphasize
- **Syntax Highlighting** - Language-specific formatting

## Content Components

### Callouts/Alerts
- **Types Available**:
  - Info (blue)
  - Warning (yellow)
  - Error (red)
  - Success (green)
  - Note (gray)
  - Tip (purple)
- **Optional Title** - Custom callout header
- **Rich Content** - Full formatting within callouts

### Tables
- **Caption** - Optional table description
- **Dynamic Rows/Columns** - Add/remove as needed
- **Header Row Option** - First row as table header
- **Cell Content** - Text content in each cell

### Dividers
- **Style Options**:
  - Line - Simple horizontal line
  - Dots - Dotted separator
  - Stars - Decorative stars
  - Spacing - Invisible spacing
- **Size Options**:
  - Small, Medium, Large

## Usage Examples

### In Sanity Schema
```typescript
{
  name: 'content',
  title: 'Article Content',
  type: 'blockContent'
}
```

### In Frontend (React/Astro)
```typescript
import { PortableText } from '@portabletext/react'

// Custom components for enhanced blocks
const components = {
  types: {
    codeBlock: CodeBlockComponent,
    callout: CalloutComponent,
    table: TableComponent,
    videoEmbed: VideoEmbedComponent,
    divider: DividerComponent,
  },
  marks: {
    highlight: ({ children }) => <mark>{children}</mark>,
    underline: ({ children }) => <u>{children}</u>,
    'strike-through': ({ children }) => <s>{children}</s>,
  },
  block: {
    h1: ({ children }) => <h1 className="text-4xl font-bold">{children}</h1>,
    h2: ({ children }) => <h2 className="text-3xl font-bold">{children}</h2>,
    // ... other heading styles
    lead: ({ children }) => <p className="text-xl text-gray-600">{children}</p>,
    small: ({ children }) => <small className="text-sm text-gray-500">{children}</small>,
  },
}

<PortableText value={content} components={components} />
```

## Benefits

### For Content Creators
- **Familiar Interface** - Similar to popular editors like CKEditor
- **Rich Formatting** - Comprehensive text styling options
- **Media Integration** - Easy image and video embedding
- **Structured Content** - Tables, callouts, and code blocks
- **Accessibility** - Built-in alt text and semantic markup

### For Developers
- **Type Safety** - Full TypeScript support
- **Flexible Rendering** - Custom component mapping
- **SEO Friendly** - Semantic HTML output
- **Performance** - Optimized portable text rendering
- **Extensible** - Easy to add new component types

### For Users
- **Better Reading Experience** - Rich, formatted content
- **Interactive Elements** - Checklists, video embeds
- **Code Examples** - Syntax-highlighted code blocks
- **Visual Hierarchy** - Clear content structure

## Customization

### Adding New Components
```typescript
// Add to blockContent.ts
defineArrayMember({
  type: "object",
  name: "customComponent",
  title: "Custom Component",
  fields: [
    // Define your fields
  ],
})
```

### Custom Decorators
```typescript
// Add to marks.decorators
{ title: "Custom Style", value: "customStyle" }
```

### New List Types
```typescript
// Add to lists array
{ title: "Custom List", value: "customList" }
```

## Best Practices

1. **Use Semantic Markup** - Choose appropriate heading levels
2. **Alt Text Required** - Always provide image descriptions
3. **Link Accessibility** - Use descriptive link text
4. **Code Documentation** - Include language and filename
5. **Table Headers** - Use header rows for data tables
6. **Callout Moderation** - Don't overuse attention-grabbing elements

## Migration from Basic Block Content

Existing content will continue to work. New features are additive:
- Old text formatting remains unchanged
- New decorators and components are optional
- Gradual adoption of enhanced features
- Backward compatibility maintained
