# Environment Variables Setup

This document explains how to set up environment variables for the Gameometry website.

## Required Environment Variables

### Sanity CMS Configuration

```bash
# Your Sanity project ID (required)
PUBLIC_SANITY_PROJECT_ID=your_sanity_project_id

# Sanity dataset (defaults to 'production' if not set)
PUBLIC_SANITY_DATASET=production

# Sanity API read token for fetching content
SANITY_API_READ_TOKEN=your_sanity_read_token

# Enable/disable visual editing (optional)
PUBLIC_SANITY_VISUAL_EDITING_ENABLED=false
```

### Email Service Configuration

```bash
# Brevo API key for sending contact form emails
BREVO_API_KEY=your_brevo_api_key
```

### Development Configuration

```bash
# Environment mode
NODE_ENV=local
```

## Setup Instructions

### 1. Copy Environment Template

```bash
cp .env.example .env
```

### 2. Update Environment Variables

Edit the `.env` file with your actual values:

```bash
# Sanity CMS Configuration
PUBLIC_SANITY_PROJECT_ID=9kfa9s6w
PUBLIC_SANITY_DATASET=development
SANITY_API_READ_TOKEN=your_actual_token_here
PUBLIC_SANITY_VISUAL_EDITING_ENABLED=true

# Email Service Configuration
BREVO_API_KEY=your_actual_brevo_key_here

# Development Configuration
NODE_ENV=local
```

### 3. Sanity Configuration

To get your Sanity credentials:

1. **Project ID**: Found in your Sanity project dashboard
2. **Dataset**: Usually 'production' or 'development'
3. **API Token**: Create a read token in your Sanity project settings

### 4. Brevo Configuration

To get your Brevo API key:

1. Sign up at [Brevo](https://www.brevo.com/)
2. Go to SMTP & API settings
3. Create a new API key
4. Copy the key to your `.env` file

## Environment Variable Loading

The application uses Vite's `loadEnv` function to load environment variables during build time. This ensures that:

- Variables are available in `astro.config.ts`
- TypeScript types are properly defined
- Build-time validation occurs

## Validation

The application validates required environment variables during startup:

- `PUBLIC_SANITY_PROJECT_ID` is required
- Missing variables will cause build failures with clear error messages

## Security Notes

- Never commit `.env` files to version control
- Use different datasets for development and production
- Rotate API tokens regularly
- Use read-only tokens when possible

## Troubleshooting

### Build Fails with "Environment variable required"

1. Check that `.env` file exists
2. Verify variable names match exactly
3. Ensure no extra spaces around values
4. Restart development server after changes

### Sanity Content Not Loading

1. Verify `PUBLIC_SANITY_PROJECT_ID` is correct
2. Check `SANITY_API_READ_TOKEN` permissions
3. Ensure dataset name matches your Sanity project

### Contact Form Not Working

1. Verify `BREVO_API_KEY` is valid
2. Check Brevo account status
3. Ensure API key has email sending permissions

## Development vs Production

### Development
```bash
PUBLIC_SANITY_DATASET=development
PUBLIC_SANITY_VISUAL_EDITING_ENABLED=true
NODE_ENV=local
```

### Production
```bash
PUBLIC_SANITY_DATASET=production
PUBLIC_SANITY_VISUAL_EDITING_ENABLED=false
NODE_ENV=production
```

## TypeScript Support

Environment variables are typed in `src/env.d.ts`:

```typescript
interface ImportMetaEnv {
  readonly PUBLIC_SANITY_PROJECT_ID: string;
  readonly PUBLIC_SANITY_DATASET: string;
  readonly SANITY_API_READ_TOKEN: string;
  readonly PUBLIC_SANITY_VISUAL_EDITING_ENABLED: string;
  readonly BREVO_API_KEY: string;
  readonly NODE_ENV: string;
}
```

This provides autocomplete and type checking in your IDE.
