# Sitemap & SEO Implementation Guide

This guide explains how to generate sitemaps with Sanity content for Google indexing and SEO optimization.

## Overview

The sitemap system automatically generates XML sitemaps that include:
- Static pages (home, about, services, contact)
- Dynamic Sanity content (publications/blog posts)
- Proper SEO metadata (lastmod, changefreq, priority)

## Files Created

### Core Utilities
- `src/utils/sitemap.ts` - Main sitemap generation logic
- `src/pages/sitemap.xml.ts` - Dynamic sitemap endpoint
- `src/pages/robots.txt.ts` - Robots.txt endpoint
- `src/pages/sitemap-index.xml.ts` - Sitemap index (for large sites)

### Build Scripts
- `scripts/generate-sitemap.js` - Manual sitemap generation script

## Configuration

### Environment Variables

Add to your `.env` file:

```bash
# Site Configuration
PUBLIC_SITE_URL=https://gameometry.io

# Sanity Configuration (existing)
PUBLIC_SANITY_PROJECT_ID=your_project_id
PUBLIC_SANITY_DATASET=production
SANITY_API_READ_TOKEN=your_read_token

# Feature Flags (existing)
PUBLIC_SHOW_PUBLICATIONS=true
```

### Deployment Configuration

For **Vercel**:
```bash
# Automatically detected
VERCEL_URL=your-app.vercel.app
```

For **Netlify**:
```bash
# Automatically detected
NETLIFY_URL=https://your-app.netlify.app
```

## Generated URLs

### Static Pages
- `https://gameometry.io/` (priority: 1.0, weekly)
- `https://gameometry.io/about` (priority: 0.8, monthly)
- `https://gameometry.io/services` (priority: 0.9, monthly)
- `https://gameometry.io/contact` (priority: 0.7, monthly)
- `https://gameometry.io/publications` (priority: 0.8, daily)

### Dynamic Sanity Content
- `https://gameometry.io/publications/[slug]` (priority: 0.6, weekly)

## Sitemap Structure

### Main Sitemap (`/sitemap.xml`)
```xml
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://gameometry.io/</loc>
    <lastmod>2024-01-15</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>https://gameometry.io/publications/mobile-game-analytics</loc>
    <lastmod>2024-01-10</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>
</urlset>
```

### Robots.txt (`/robots.txt`)
```txt
User-agent: *
Allow: /

Sitemap: https://gameometry.io/sitemap.xml

Disallow: /admin/
Disallow: /api/
Disallow: /studio/
Disallow: /_astro/
```

## How It Works

### 1. Data Fetching
```typescript
// Fetch all published posts from Sanity
const { data: posts } = await loadQuery<SanityPost[]>({
  query: SITEMAP_POSTS_QUERY,
});
```

### 2. URL Generation
```typescript
// Generate URLs for each post
const publicationUrls = posts.map(post => ({
  loc: `${baseUrl}/publications/${post.slug.current}`,
  lastmod: post._updatedAt || post.publishedAt,
  changefreq: 'weekly',
  priority: 0.6,
}));
```

### 3. XML Generation
```typescript
// Generate XML sitemap
const xml = generateSitemapXml([
  ...staticPages,
  ...publicationUrls,
]);
```

## SEO Benefits

### For Google
- **Faster Discovery**: Google finds new content quickly
- **Better Indexing**: All pages are properly catalogued
- **Update Notifications**: lastmod tells Google when content changed
- **Priority Signals**: Important pages get higher priority

### For Users
- **Better Search Results**: More pages appear in search
- **Faster Updates**: New content appears in search faster
- **Improved Rankings**: Proper SEO structure helps rankings

## Implementation Steps

### 1. Set Environment Variables
```bash
# Add to your .env file
PUBLIC_SITE_URL=https://gameometry.io
```

### 2. Deploy the Code
The sitemap endpoints are automatically available at:
- `https://gameometry.io/sitemap.xml`
- `https://gameometry.io/robots.txt`

### 3. Submit to Google
1. Go to [Google Search Console](https://search.google.com/search-console)
2. Add your property (gameometry.io)
3. Go to "Sitemaps" in the left menu
4. Add sitemap URL: `https://gameometry.io/sitemap.xml`
5. Click "Submit"

### 4. Verify Indexing
- Check Google Search Console for indexing status
- Use `site:gameometry.io` in Google to see indexed pages
- Monitor for crawl errors or issues

## Advanced Features

### Dynamic Updates
The sitemap automatically updates when:
- New posts are published in Sanity
- Existing posts are modified
- Feature flags change (publications enabled/disabled)

### Caching
- Sitemap cached for 1 hour
- Robots.txt cached for 24 hours
- Reduces server load while staying current

### Error Handling
- Graceful fallbacks if Sanity is unavailable
- Proper HTTP status codes
- Detailed error logging

## Monitoring & Maintenance

### Google Search Console
Monitor these metrics:
- **Submitted URLs**: Total URLs in sitemap
- **Indexed URLs**: URLs Google has indexed
- **Coverage Issues**: Errors or warnings
- **Performance**: Click-through rates and impressions

### Regular Checks
1. **Monthly**: Review indexed pages count
2. **Weekly**: Check for crawl errors
3. **After content updates**: Verify new pages are indexed

### Troubleshooting

#### Sitemap Not Loading
1. Check environment variables are set
2. Verify Sanity connection is working
3. Check server logs for errors

#### Pages Not Indexed
1. Verify URLs are in sitemap
2. Check robots.txt isn't blocking
3. Ensure pages return 200 status
4. Check for duplicate content issues

#### Slow Indexing
1. Submit individual URLs in Search Console
2. Check page loading speed
3. Verify internal linking structure
4. Ensure content quality is high

## Best Practices

### Content Strategy
- **Regular Updates**: Publish content consistently
- **Quality Content**: Focus on valuable, unique content
- **Internal Linking**: Link between related pages
- **Mobile Optimization**: Ensure mobile-friendly design

### Technical SEO
- **Fast Loading**: Optimize page speed
- **HTTPS**: Use secure connections
- **Structured Data**: Add schema markup
- **Meta Tags**: Proper title and description tags

### Monitoring
- **Set Up Alerts**: Get notified of indexing issues
- **Regular Audits**: Monthly SEO health checks
- **Performance Tracking**: Monitor search rankings
- **User Experience**: Track bounce rates and engagement

## Next Steps

1. **Deploy the sitemap system**
2. **Set up Google Search Console**
3. **Submit your sitemap**
4. **Monitor indexing progress**
5. **Optimize based on Search Console data**

This implementation provides a solid foundation for SEO success with automatic sitemap generation from your Sanity content.
