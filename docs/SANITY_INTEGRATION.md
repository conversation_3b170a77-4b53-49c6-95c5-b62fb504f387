# Sanity Integration Guide

This document explains how to use the extended SanityDocument interfaces and utilities in your Astro project.

## Overview

We've created TypeScript interfaces that extend the base `SanityDocument` to provide type safety when working with Sanity CMS data. These interfaces are based on the schema definitions in `src/sanity/schemaTypes/`.

## Interfaces

### Base SanityDocument

```typescript
export interface SanityDocument {
  _id: string;
  _type: string;
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
}
```

### SanityPost Interface

Based on `src/sanity/schemaTypes/post.ts`:

```typescript
export interface SanityPost extends SanityDocument {
  _type: 'post';
  title: string;
  slug: SanitySlug;
  author?: SanityReference | SanityAuthor;
  mainImage?: SanityImageAsset;
  categories?: (SanityReference | SanityCategory)[];
  publishedAt?: string;
  body?: PortableTextBlock[];
}
```

### Supporting Interfaces

- `SanityAuthor` - Author document type
- `SanityCategory` - Category document type
- `SanityImageAsset` - Image with hotspot and crop data
- `SanitySlug` - Slug field type
- `SanityReference` - Reference field type

## Usage Examples

### 1. Fetching Posts from Sanity

```typescript
import { loadQuery } from '~/sanity/lib/load-query';
import { POSTS_QUERY, sanityPostsToPosts } from '~/utils/sanity';
import type { SanityPost, Post } from '~/types';

// Fetch posts with populated references
const { data: sanityPosts } = await loadQuery<SanityPost[]>({
  query: POSTS_QUERY,
});

// Convert to standard Post format
const posts: Post[] = sanityPostsToPosts(sanityPosts || []);
```

### 2. Using GROQ Queries

Pre-defined queries are available in `src/utils/sanity.ts`:

```typescript
// All posts with populated references
export const POSTS_QUERY = `
  *[_type == "post"] | order(publishedAt desc) {
    _id,
    _type,
    title,
    slug,
    "author": author->{...},
    mainImage,
    "categories": categories[]->{...},
    publishedAt,
    body
  }
`;

// Single post by slug
export const POST_BY_SLUG_QUERY = `...`;

// Posts by category
export const POSTS_BY_CATEGORY_QUERY = `...`;
```

### 3. Converting Sanity Data

The `sanityPostToPost()` function converts Sanity posts to the standard Post interface:

```typescript
import { sanityPostToPost } from '~/utils/sanity';

const sanityPost: SanityPost = { /* ... */ };
const post: Post = sanityPostToPost(sanityPost);
```

### 4. Working with Images

```typescript
import { urlForImage } from '~/sanity/lib/url-for-image';

const imageUrl = sanityPost.mainImage 
  ? urlForImage(sanityPost.mainImage)?.url() 
  : undefined;
```

## File Structure

```
src/
├── types.d.ts                 # All TypeScript interfaces
├── utils/sanity.ts           # Conversion utilities and GROQ queries
├── pages/posts/index.astro   # Example usage
└── sanity/
    ├── schemaTypes/
    │   ├── post.ts           # Post schema definition
    │   ├── author.ts         # Author schema definition
    │   └── category.ts       # Category schema definition
    └── lib/
        ├── load-query.ts     # Query loading utility
        └── url-for-image.ts  # Image URL generation
```

## Benefits

1. **Type Safety**: Full TypeScript support for Sanity data
2. **Consistency**: Unified interface between Sanity and existing Post types
3. **Flexibility**: Easy to extend for new schema fields
4. **Maintainability**: Centralized type definitions and utilities
5. **Developer Experience**: Better autocomplete and error checking

## Adding New Fields

To add new fields to the post schema:

1. Update `src/sanity/schemaTypes/post.ts`
2. Update the `SanityPost` interface in `src/types.d.ts`
3. Update the conversion logic in `src/utils/sanity.ts`
4. Update the GROQ queries if needed

## Example: Adding an Excerpt Field

1. **Schema** (`src/sanity/schemaTypes/post.ts`):
```typescript
defineField({
  name: "excerpt",
  type: "text",
  title: "Excerpt",
}),
```

2. **Interface** (`src/types.d.ts`):
```typescript
export interface SanityPost extends SanityDocument {
  // ... existing fields
  excerpt?: string;
}
```

3. **Conversion** (`src/utils/sanity.ts`):
```typescript
export function sanityPostToPost(sanityPost: SanityPost): Post {
  return {
    // ... existing fields
    excerpt: sanityPost.excerpt,
  };
}
```

4. **Query** (`src/utils/sanity.ts`):
```typescript
export const POSTS_QUERY = `
  *[_type == "post"] | order(publishedAt desc) {
    // ... existing fields
    excerpt,
  }
`;
```
