{"name": "gameometry", "version": "1.0.0-beta.51", "description": "Gameometry: A free template using Astro 5.0 and Tailwind CSS. Astro starter theme.", "type": "module", "private": true, "engines": {"node": "^18.17.1 || ^20.3.0 || >= 21.0.0"}, "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "check": "npm run check:astro && npm run check:eslint && npm run check:prettier", "check:astro": "astro check", "check:eslint": "eslint .", "check:prettier": "prettier --check .", "fix": "npm run fix:eslint && npm run fix:prettier", "fix:eslint": "eslint --fix .", "fix:prettier": "prettier -w ."}, "dependencies": {"@astrojs/node": "^9.1.3", "@astrojs/react": "^4.3.0", "@astrojs/rss": "^4.0.11", "@astrojs/sitemap": "^3.2.1", "@astrojs/vercel": "^8.1.3", "@astrolib/analytics": "^0.6.1", "@astrolib/seo": "^1.0.0-beta.8", "@fontsource-variable/inter": "^5.2.5", "@sanity/astro": "^3.2.6", "@sanity/client": "^6.29.1", "@sanity/image-url": "^1.1.0", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "astro": "^5.5.2", "astro-embed": "^0.9.0", "astro-icon": "^1.1.5", "astro-portabletext": "^0.11.1", "limax": "4.1.0", "lodash.merge": "^4.6.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-is": "^19.1.0", "sanity": "^3.90.0", "styled-components": "^6.1.18", "unpic": "^4.1.2"}, "devDependencies": {"@astrojs/check": "^0.9.4", "@astrojs/mdx": "^4.2.0", "@astrojs/partytown": "^2.1.4", "@astrojs/tailwind": "^5.1.5", "@eslint/js": "^9.22.0", "@iconify-json/flat-color-icons": "^1.2.1", "@iconify-json/tabler": "^1.2.17", "@tailwindcss/typography": "^0.5.16", "@types/js-yaml": "^4.0.9", "@types/lodash.merge": "^4.6.9", "@types/mdx": "^2.0.13", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "astro-compress": "2.3.6", "astro-eslint-parser": "^1.2.1", "eslint": "^9.22.0", "eslint-plugin-astro": "^1.3.1", "globals": "^16.0.0", "js-yaml": "^4.1.0", "mdast-util-to-string": "^4.0.0", "prettier": "^3.5.3", "prettier-plugin-astro": "^0.14.1", "reading-time": "^1.5.0", "sharp": "0.33.5", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.17", "typescript": "^5.8.2", "typescript-eslint": "^8.26.1", "unist-util-visit": "^5.0.0"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5"}}