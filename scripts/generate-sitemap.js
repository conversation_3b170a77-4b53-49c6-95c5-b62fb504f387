#!/usr/bin/env node

/**
 * <PERSON>ript to generate sitemap.xml during build process
 * This can be run manually or as part of the build pipeline
 */

import { writeFileSync } from 'fs';
import { join } from 'path';

// Import the sitemap utilities
// Note: This would need to be adapted for your build environment
async function generateSitemap() {
  try {
    console.log('🗺️  Generating sitemap...');
    
    // This would need to be adapted to work with your build process
    // For now, this is a placeholder that shows the concept
    
    const sitemapContent = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://gameometry.io/</loc>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>https://gameometry.io/about</loc>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>https://gameometry.io/services</loc>
    <changefreq>monthly</changefreq>
    <priority>0.9</priority>
  </url>
  <url>
    <loc>https://gameometry.io/contact</loc>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>
  <url>
    <loc>https://gameometry.io/publications</loc>
    <changefreq>daily</changefreq>
    <priority>0.8</priority>
  </url>
</urlset>`;

    // Write to public directory
    const outputPath = join(process.cwd(), 'public', 'sitemap.xml');
    writeFileSync(outputPath, sitemapContent, 'utf8');
    
    console.log('✅ Sitemap generated successfully at:', outputPath);
    
    // Also generate robots.txt
    const robotsContent = `User-agent: *
Allow: /

Sitemap: https://gameometry.io/sitemap.xml

Disallow: /admin/
Disallow: /api/
Disallow: /studio/
Disallow: /_astro/`;

    const robotsPath = join(process.cwd(), 'public', 'robots.txt');
    writeFileSync(robotsPath, robotsContent, 'utf8');
    
    console.log('✅ Robots.txt generated successfully at:', robotsPath);
    
  } catch (error) {
    console.error('❌ Error generating sitemap:', error);
    process.exit(1);
  }
}

// Run the script
generateSitemap();
