site:
  name: Gameometry
  site: "https://gameometry.io"
  base: "/"
  trailingSlash: false

  googleSiteVerificationId: xxx

# Default SEO metadata
metadata:
  title:
    default: Gameometry
    template: "%s — Gameometry"
  description: "\U0001F680 Empowering game makers to unlock their full potential"
  robots:
    index: true
    follow: true
  openGraph:
    site_name: Gameometry
    images:
      - url: "~/assets/images/default.png"
        width: 1200
        height: 628
    type: website
  twitter:
    handle: "@gameometry"
    site: "@gameometry"
    cardType: summary_large_image

i18n:
  language: en
  textDirection: ltr

apps:
  blog:
    isEnabled: false
    postsPerPage: 6

    post:
      isEnabled: true
      permalink: "/%slug%" # Variables: %slug%, %year%, %month%, %day%, %hour%, %minute%, %second%, %category%
      robots:
        index: true

    list:
      isEnabled: false
      pathname: "blog" # Blog main path, you can change this to "articles" (/articles)
      robots:
        index: true

    category:
      isEnabled: false
      pathname: "category" # Category main path /category/some-category, you can change this to "group" (/group/some-category)
      robots:
        index: true

    tag:
      isEnabled: false
      pathname: "tag" # Tag main path /tag/some-tag, you can change this to "topics" (/topics/some-category)
      robots:
        index: false

    isRelatedPostsEnabled: true
    relatedPostsCount: 4

analytics:
  vendors:
    googleAnalytics:
      id: "G-KR2EXCC9HS"

ui:
  theme: "system" # Values: "system" | "light" | "dark" | "light:only" | "dark:only"
