---
import type { InferGetStaticPropsType, GetStaticPaths } from 'astro';

import Layout from '~/layouts/PageLayout.astro';
import BlogList from '~/components/blog/List.astro';
import Headline from '~/components/blog/Headline.astro';
import Pagination from '~/components/blog/Pagination.astro';
// import PostTags from "~/components/blog/Tags.astro";

import { blogListRobots, getStaticPathsBlogList } from '~/utils/blog';

export const prerender = true;

export const getStaticPaths = (async ({ paginate }) => {
  return await getStaticPathsBlogList({ paginate });
}) satisfies GetStaticPaths;

type Props = InferGetStaticPropsType<typeof getStaticPaths>;

const { page } = Astro.props as Props;
const currentPage = page.currentPage ?? 1;

// const allCategories = await findCategories();
// const allTags = await findTags();

const metadata = {
  title: `Blog${currentPage > 1 ? ` — Page ${currentPage}` : ''}`,
  robots: {
    index: blogListRobots?.index && currentPage === 1,
    follow: blogListRobots?.follow,
  },
  openGraph: {
    type: 'blog',
  },
};
---

<Layout metadata={metadata}>
  <section class="px-6 sm:px-6 py-12 sm:py-16 lg:py-20 mx-auto max-w-4xl">
    <Headline
      subtitle="A statically generated blog example with news, tutorials, resources and other interesting content related to Gameometry"
    >
      The Blog
    </Headline>
    <BlogList posts={page.data} />
    <Pagination prevUrl={page.url.prev} nextUrl={page.url.next} />
    <!--
      <PostTags tags={allCategories} class="mb-2" title="Search by Categories:" isCategory />
      <PostTags tags={allTags}  title="Search by Tags:" />
    -->
  </section>
</Layout>
