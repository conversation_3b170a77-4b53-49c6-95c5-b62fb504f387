---
import Layout from '~/layouts/PageLayout.astro';
import Header from '~/components/widgets/Header.astro';

import { headerData } from '~/navigation';
import ServicesContent1 from '~/components/widgets/ServicesContent1.astro';

// Import the compressed banner image
import mainBanner from '~/assets/images/main-banner-compressed.jpg';

const metadata = {
  title: 'Gameometry - A Mobile Games Consulting Firm',
};
---

<style>
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in {
    animation: fadeIn 0.8s ease-out forwards;
  }
</style>

<Layout metadata={metadata}>
  <Fragment slot="header">
    <Header {...headerData} isSticky showToggleTheme={true} />
  </Fragment>

  <!-- Full-width Hero with Background Image -->

  <section id="main-banner" class="relative w-full min-h-screen flex items-center justify-center overflow-hidden">
    <!-- Background Image with Overlay -->
    <div class="absolute inset-0 z-0">
      <img
        src={mainBanner.src}
        alt="Gaming Controller with Neon Lights"
        class="w-full h-full object-cover"
        width={mainBanner.width}
        height={mainBanner.height}
      />
      <div class="absolute inset-0 bg-gradient-to-b from-gray-900/80 via-gray-900/70 to-primary/30"></div>
    </div>

    <!-- Content Overlay -->
    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 py-20 text-center text-white">
      <p
        class="text-base text-blue-400 font-bold tracking-wide uppercase mb-4 animate-fade-in opacity-0"
        style="animation-delay: 0.2s; animation-fill-mode: forwards;"
      >
        Empowering game makers to unlock their full potential
      </p>

      <h1
        class="text-5xl md:text-6xl font-bold leading-tighter tracking-tighter mb-8 font-heading animate-fade-in opacity-0"
        style="animation-delay: 0.4s; animation-fill-mode: forwards;"
      >
        A Mobile Games Consulting Firm
      </h1>

      <div class="max-w-3xl mx-auto">
        <p
          class="text-3xl mb-6 text-blue-100 animate-fade-in opacity-0"
          style="animation-delay: 0.6s; animation-fill-mode: forwards;"
        >
          focused on servicing the mobile games industry by supporting the product and UA strategy
        </p>

        <div
          class="mt-12 flex flex-wrap justify-center gap-6 animate-fade-in opacity-0"
          style="animation-delay: 1s; animation-fill-mode: forwards;"
        >
          <a
            href="/about"
            class="px-10 py-4 text-lg font-medium rounded-full bg-blue-600 hover:bg-blue-500 transition-all duration-300 shadow-lg hover:shadow-blue-500/50 transform hover:-translate-y-1"
            >About Us</a
          >
        </div>
      </div>
    </div>
  </section>

  <!-- Services Section -->
  <ServicesContent1 />
</Layout>
