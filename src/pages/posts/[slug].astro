---
// ./src/pages/post/[slug].astro
import type { SanityDocument } from '@sanity/client';
import { loadQuery } from '~/sanity/lib/load-query';
import Layout from '~/layouts/PageLayout.astro';
import PortableText from '~/components/common/PortableText.astro';

/* TODO: can be removed as its dynamic and not static */
export async function getStaticPaths() {
  const { data: posts } = await loadQuery<SanityDocument[]>({
    query: `*[_type == "post"]`,
  });

  return posts.map(({ slug }) => {
    return {
      params: {
        slug: slug.current,
      },
    };
  });
}

const { params } = Astro;

const { data: post } = await loadQuery<{ title: string; body: any[] }>({
  query: `*[_type == "post" && slug.current == $slug][0]`,
  params,
});
---

<Layout>
  <h1>A post about {post.title}</h1>
  <PortableText portableText={post.body} />
</Layout>
