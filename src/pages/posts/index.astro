---
import Layout from '~/layouts/PageLayout.astro';
import type { SanityDocument } from '@sanity/client';
import { loadQuery } from '~/sanity/lib/load-query';
import PostsList from '~/components/blog/PostsList.astro';
import PostsGrid from '~/components/blog/PostsGrid.astro';
import Headline from '~/components/blog/Headline.astro';

import type { MetaData } from '~/types';

// Fetch all posts
const { data: posts } = await loadQuery<SanityDocument[]>({
  query: `*[_type == "post"]`,
});

console.log(posts);

const metadata: MetaData = {
  title: 'Publications - Gameometry',
  description:
    'Explore our latest insights, research, and thought leadership in mobile gaming, user acquisition, and game development.',
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: 'website',
  },
};
---

<Layout metadata={metadata}>
  <section class="px-6 sm:px-6 py-12 sm:py-16 lg:py-20 mx-auto max-w-6xl">
    <Headline
      classes={{
        container: 'text-center mb-12',
        title: 'text-2xl md:text-2xl font-bold leading-tighter tracking-tighter mb-2 font-heading',
      }}
    >
      Publications
    </Headline>

    <!-- View Toggle -->
    <div class="flex justify-center mb-8">
      <div class="bg-gray-100 rounded-lg p-1 flex">
        <button
          id="list-view-btn"
          class="px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 bg-white text-gray-900 shadow-sm"
          onclick="showListView()"
        >
          List View
        </button>
        <button
          id="grid-view-btn"
          class="px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 text-gray-600 hover:text-gray-900"
          onclick="showGridView()"
        >
          Grid View
        </button>
      </div>
    </div>

    {
      posts.length > 0 ? (
        <>
          <div id="list-view">
            <PostsList posts={posts} />
          </div>
          <div id="grid-view" class="hidden">
            <PostsGrid posts={posts} />
          </div>
        </>
      ) : (
        <div class="text-center py-12">
          <p class="text-xl text-muted">No publications available at the moment.</p>
          <p class="text-muted mt-2">Check back soon for new insights and research.</p>
        </div>
      )
    }
  </section>
</Layout>

<script>
  function showListView() {
    const listView = document.getElementById('list-view');
    const gridView = document.getElementById('grid-view');
    const listBtn = document.getElementById('list-view-btn');
    const gridBtn = document.getElementById('grid-view-btn');

    if (listView && gridView && listBtn && gridBtn) {
      listView.classList.remove('hidden');
      gridView.classList.add('hidden');

      listBtn.classList.add('bg-white', 'text-gray-900', 'shadow-sm');
      listBtn.classList.remove('text-gray-600', 'hover:text-gray-900');

      gridBtn.classList.remove('bg-white', 'text-gray-900', 'shadow-sm');
      gridBtn.classList.add('text-gray-600', 'hover:text-gray-900');
    }
  }

  function showGridView() {
    const listView = document.getElementById('list-view');
    const gridView = document.getElementById('grid-view');
    const listBtn = document.getElementById('list-view-btn');
    const gridBtn = document.getElementById('grid-view-btn');

    if (listView && gridView && listBtn && gridBtn) {
      listView.classList.add('hidden');
      gridView.classList.remove('hidden');

      gridBtn.classList.add('bg-white', 'text-gray-900', 'shadow-sm');
      gridBtn.classList.remove('text-gray-600', 'hover:text-gray-900');

      listBtn.classList.remove('bg-white', 'text-gray-900', 'shadow-sm');
      listBtn.classList.add('text-gray-600', 'hover:text-gray-900');
    }
  }

  // Make functions globally available
  declare global {
    interface Window {
      showListView: () => void;
      showGridView: () => void;
    }
  }

  window.showListView = showListView;
  window.showGridView = showGridView;
</script>
