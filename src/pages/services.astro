---
import ServicesContent1 from '~/components/widgets/ServicesContent1.astro';
import Layout from '~/layouts/PageLayout.astro';
import Header from '~/components/widgets/Header.astro';

import { headerData } from '~/navigation';

const metadata = {
  title: 'Our Services | Gameometry',
};
---

<Layout metadata={metadata}>
  <Fragment slot="header">
    <Header {...headerData} isSticky showToggleTheme={true} />
  </Fragment>

  <!-- About Hero Section -->
  <section class="relative py-16 md:py-20 lg:py-24 bg-[#F2F2F2]">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <div>
          <h1 class="text-4xl md:text-5xl font-bold mb-6">A one-stop shop to deliver growth strategy</h1>
          <p class="text-xl text-muted mb-8">
            We are a full strategy team to tackle every aspect in the mobile games market
          </p>
        </div>
        <div class="relative overflow-hidden rounded-lg">
          <img
            src="/images/services-cycle.jpeg"
            alt="Gameometry Services Cycle"
            class="w-full h-auto mx-auto max-w-4xl transform transition-transform duration-700 hover:scale-105"
          />

          <!-- Animated Overlay Elements -->
          <div class="absolute top-0 left-0 w-full h-full pointer-events-none">
            <!-- Pulsing Circles at Strategic Points -->
            <div class="absolute top-1/4 left-1/4 w-4 h-4 bg-primary/30 rounded-full animate-ping"></div>
            <div
              class="absolute top-1/3 right-1/4 w-3 h-3 bg-casino/30 rounded-full animate-ping"
              style="animation-delay: 0.5s"
            >
            </div>
            <div
              class="absolute bottom-1/4 right-1/3 w-5 h-5 bg-geometry/30 rounded-full animate-ping"
              style="animation-delay: 1s"
            >
            </div>
            <div
              class="absolute bottom-1/3 left-1/3 w-4 h-4 bg-consultancy/30 rounded-full animate-ping"
              style="animation-delay: 1.5s"
            >
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Services Section -->
  <ServicesContent1 />
  <style>
    @keyframes ping {
      0% {
        transform: scale(0.8);
        opacity: 0.8;
      }
      70% {
        transform: scale(2);
        opacity: 0;
      }
      100% {
        transform: scale(0.8);
        opacity: 0;
      }
    }

    .animate-ping {
      animation: ping 3s cubic-bezier(0, 0, 0.2, 1) infinite;
    }
  </style>
</Layout>
