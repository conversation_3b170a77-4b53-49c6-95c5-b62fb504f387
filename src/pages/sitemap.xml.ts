import type { APIRoute } from 'astro';
import { generateSitemapUrls, generateSitemapXml } from '~/utils/sitemap';

export const GET: APIRoute = async () => {
  try {
    // Generate all sitemap URLs
    const urls = await generateSitemapUrls();
    
    // Generate XML content
    const xml = generateSitemapXml(urls);
    
    // Return XML response with proper headers
    return new Response(xml, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml; charset=utf-8',
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      },
    });
  } catch (error) {
    console.error('Error generating sitemap:', error);
    
    // Return error response
    return new Response('Error generating sitemap', {
      status: 500,
      headers: {
        'Content-Type': 'text/plain',
      },
    });
  }
};

// Pre-render the sitemap at build time for better performance
export const prerender = false; // Set to true if you want static generation
