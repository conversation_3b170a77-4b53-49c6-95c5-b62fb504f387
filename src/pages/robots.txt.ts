import type { APIRoute } from 'astro';
import { generateRobotsTxt } from '~/utils/sitemap';

export const GET: APIRoute = async () => {
  try {
    // Generate robots.txt content
    const robotsTxt = generateRobotsTxt();
    
    // Return text response with proper headers
    return new Response(robotsTxt, {
      status: 200,
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
      },
    });
  } catch (error) {
    console.error('Error generating robots.txt:', error);
    
    // Return error response
    return new Response('Error generating robots.txt', {
      status: 500,
      headers: {
        'Content-Type': 'text/plain',
      },
    });
  }
};

// Pre-render the robots.txt at build time
export const prerender = true;
