---
import Layout from '~/layouts/PageLayout.astro';
import ContactUs from '~/components/widgets/Contact.astro';
import Header from '~/components/widgets/Header.astro';

import { headerData } from '~/navigation';

const metadata = {
  title: 'Contact Gameometry | Get in Touch',
};
---

<Layout metadata={metadata}>
  <Fragment slot="header">
    <Header {...headerData} isSticky showToggleTheme={true} />
  </Fragment>

  <!-- Contact Information and Form Section -->
  <section class="py-12 md:py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Contact Information -->
        <div class="bg-[#F2F2F2] p-5 rounded-lg self-start max-w-md">
          <h2 class="text-xl font-bold mb-3">Get In Touch</h2>

          <div class="space-y-3">
            <!-- Email Contact -->
            <div class="flex items-start">
              <div class="flex-shrink-0 mt-1">
                <div class="bg-primary text-white p-2 rounded-full">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    ></path>
                  </svg>
                </div>
              </div>
              <div class="ml-3">
                <h3 class="text-base font-semibold">Email</h3>
                <p class="text-sm text-muted mt-0.5">For general inquiries:</p>
                <a href="mailto:<EMAIL>" class="text-primary hover:underline text-sm"><EMAIL></a>
              </div>
            </div>

            <!-- Website -->
            <div class="flex items-start">
              <div class="flex-shrink-0 mt-1">
                <div class="bg-casino text-white p-2 rounded-full">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"
                    ></path>
                  </svg>
                </div>
              </div>
              <div class="ml-3">
                <h3 class="text-base font-semibold">Website</h3>
                <p class="text-sm text-muted mt-0.5">Visit our website:</p>
                <a href="https://gameometry.io" class="text-casino hover:underline text-sm">www.gameometry.io</a>
              </div>
            </div>
          </div>
        </div>

        <!-- Contact Form -->
        <div>
          <h2 class="text-xl font-bold mb-3">Send Us a Message</h2>
          <ContactUs
            id="form"
            inputs={[
              {
                type: 'text',
                name: 'name',
                placeholder: 'Your Name',
                required: true,
              },
              {
                type: 'email',
                name: 'email',
                placeholder: 'Your Email',
                required: true,
              },
              {
                type: 'text',
                name: 'company',
                placeholder: 'Your Company',
                required: false,
              },
              {
                type: 'text',
                name: 'subject',
                placeholder: 'Subject',
                required: true,
              },
            ]}
            textarea={{
              placeholder: 'Your Message',
              rows: 4,
            }}
            button="Send Message"
          />
        </div>
      </div>
    </div>
  </section>
</Layout>
