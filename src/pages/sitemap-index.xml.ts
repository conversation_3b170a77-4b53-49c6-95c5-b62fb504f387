import type { APIRoute } from 'astro';
import { getBaseUrl } from '~/utils/sitemap';

export const GET: APIRoute = async () => {
  try {
    const baseUrl = getBaseUrl();
    
    // Generate sitemap index XML
    const xml = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <sitemap>
    <loc>${baseUrl}/sitemap.xml</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
  </sitemap>
</sitemapindex>`;
    
    // Return XML response with proper headers
    return new Response(xml, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml; charset=utf-8',
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      },
    });
  } catch (error) {
    console.error('Error generating sitemap index:', error);
    
    // Return error response
    return new Response('Error generating sitemap index', {
      status: 500,
      headers: {
        'Content-Type': 'text/plain',
      },
    });
  }
};

// Pre-render the sitemap index at build time
export const prerender = true;
