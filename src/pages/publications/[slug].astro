---
import { loadQuery } from '~/sanity/lib/load-query';
import Layout from '~/layouts/PageLayout.astro';
import PortableText from '~/components/common/PortableText.astro';
import { Icon } from 'astro-icon/components';
import Image from '~/components/common/Image.astro';

import type { MetaData, SanityPost } from '~/types';
import { POST_BY_SLUG_QUERY } from '~/utils/sanity';
import { urlForImage } from '~/sanity/lib/url-for-image';
import { getFormattedDate } from '~/utils/utils';

export async function getStaticPaths() {
  const { data: posts } = await loadQuery<SanityPost[]>({
    query: `*[_type == "post"]{ _id, slug }`,
  });

  return posts.map((post) => {
    return {
      params: {
        slug: post.slug?.current || '',
      },
    };
  });
}

const { slug } = Astro.params;

if (!slug) {
  return Astro.redirect('/publications');
}

const { data: post } = await loadQuery<SanityPost>({
  query: POST_BY_SLUG_QUERY,
  params: { slug },
});

if (!post) {
  return Astro.redirect('/publications');
}

// Generate image URL if available
const imageUrl = post.mainImage ? urlForImage(post.mainImage)?.url() : undefined;

// Get author name
const authorName = post.author &&
  typeof post.author === 'object' &&
  post.author !== null &&
  'name' in post.author
  ? (post.author as any).name
  : 'Gameometry Team';

// Get categories
const categories = post.categories
  ?.filter((cat) => cat && typeof cat === 'object' && cat !== null && 'title' in cat)
  .map((cat) => (cat as any).title) || [];

const metadata: MetaData = {
  title: `${post.title} - Gameometry`,
  description: post.body ? 'Read our latest insights on mobile gaming and user acquisition.' : undefined,
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: 'article',
    ...(imageUrl
      ? {
          images: [
            {
              url: imageUrl,
              width: 1200,
              height: 630,
            },
          ],
        }
      : {}),
  },
};
---

<Layout metadata={metadata}>
  <article class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20">
    <!-- Back to Publications Link -->
    <div class="mb-8">
      <a href="/publications" class="inline-flex items-center text-primary hover:text-primary-dark font-medium">
        <Icon name="tabler:arrow-left" class="w-4 h-4 mr-2" />
        Back to Publications
      </a>
    </div>

    <!-- Article Header -->
    <header class="mb-8">
      {
        categories.length > 0 && (
          <div class="mb-4">
            {categories.map((category) => (
              <span class="inline-block bg-primary text-white px-3 py-1 rounded-full text-sm font-medium mr-2">
                {category}
              </span>
            ))}
          </div>
        )
      }

      <h1 class="text-3xl md:text-4xl lg:text-5xl font-bold leading-tight mb-6">
        {post.title}
      </h1>

      <div class="flex items-center text-muted text-sm space-x-4">
        <div class="flex items-center">
          <Icon name="tabler:user" class="w-4 h-4 mr-2" />
          <span>{authorName}</span>
        </div>

        {
          post.publishedAt && (
            <div class="flex items-center">
              <Icon name="tabler:calendar" class="w-4 h-4 mr-2" />
              <time datetime={post.publishedAt}>{getFormattedDate(new Date(post.publishedAt))}</time>
            </div>
          )
        }
      </div>
    </header>

    <!-- Featured Image -->
    {
      imageUrl && (
        <div class="mb-8">
          <Image
            src={imageUrl}
            alt={post.mainImage?.alt || post.title}
            class="w-full h-64 md:h-96 object-cover rounded-lg"
            width={1200}
            height={630}
            loading="eager"
          />
        </div>
      )
    }

    <!-- Article Content -->
    <div class="prose prose-lg max-w-none">
      {post.body && <PortableText portableText={post.body} />}
    </div>

    <!-- Article Footer -->
    <footer class="mt-12 pt-8 border-t border-gray-200">
      <div class="flex items-center justify-between">
        <div class="text-sm text-muted">
          Published by {authorName}
          {post.publishedAt && <span> on {getFormattedDate(new Date(post.publishedAt))}</span>}
        </div>

        <a href="/publications" class="inline-flex items-center text-primary hover:text-primary-dark font-medium">
          View all publications
          <Icon name="tabler:arrow-right" class="w-4 h-4 ml-2" />
        </a>
      </div>
    </footer>
  </article>
</Layout>
