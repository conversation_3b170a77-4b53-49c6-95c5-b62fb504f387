---
import Layout from '~/layouts/PageLayout.astro';
import { loadQuery } from '~/sanity/lib/load-query';
import PostsList from '~/components/blog/PostsList.astro';
import PostsGrid from '~/components/blog/PostsGrid.astro';
import Header from '~/components/widgets/Header.astro';
import SanityLoader from '~/components/ui/SanityLoader.astro';
import PublicationsLoader from '~/components/ui/PublicationsLoader.astro';

import { headerData } from '~/navigation';
import type { MetaData, SanityPost, Post } from '~/types';
import { POSTS_QUERY, sanityPostsToPosts } from '~/utils/sanity';
import { isPublicationsEnabled } from '~/utils/feature-flags';

// Check if publications feature is enabled
const publicationsEnabled = isPublicationsEnabled();

// Fetch all posts from Sanity only if publications are enabled
let posts: Post[] = [];
let isLoading = true;
let loadingError: string | null = null;

if (publicationsEnabled) {
  try {
    console.log('🔄 Starting Sanity data fetch...');
    const startTime = Date.now();

    const { data: sanityPosts } = await loadQuery<SanityPost[]>({
      query: POSTS_QUERY,
    });

    const loadTime = Date.now() - startTime;
    console.log(`✅ Sanity data loaded in ${loadTime}ms`);

    // Convert Sanity posts to standard Post format
    posts = sanityPostsToPosts(sanityPosts || []);
    console.log(`📄 Converted ${posts.length} posts`);

    isLoading = false;
  } catch (error) {
    console.error('❌ Error loading Sanity data:', error);
    loadingError = 'Failed to load publications. Please try again later.';
    isLoading = false;
  }
} else {
  isLoading = false;
}
const metadata: MetaData = {
  title: 'Publications - Gameometry',
  description:
    'Explore our latest insights, research, and thought leadership in mobile gaming, user acquisition, and game development.',
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: 'website',
  },
};
---

<Layout metadata={metadata}>
  <Fragment slot="header">
    <Header {...headerData} isSticky showToggleTheme={true} />
  </Fragment>

  <!-- Page Header -->
  <section class="px-6 sm:px-6 py-8 sm:py-12 mx-auto max-w-7xl">
    <div class="text-center mb-8">
      <h1 class="text-4xl md:text-5xl font-bold leading-tighter tracking-tighter mb-4 font-heading">Publications</h1>
      <p class="text-xl text-muted max-w-3xl mx-auto">
        Explore our latest insights, research, and thought leadership in mobile gaming, user acquisition, and game
        development.
      </p>
    </div>
  </section>

  <!-- Main Content Section -->
  <section class="px-6 sm:px-6 pb-12 sm:pb-16 lg:pb-20 mx-auto max-w-7xl">
    <div class="flex flex-col lg:flex-row gap-8">
      <!-- Left Sidebar -->
      <aside class="lg:w-64 flex-shrink-0">
        <div class="sticky top-24">
          {publicationsEnabled && posts.length > 0 && (
            <>
              <!-- View Toggle -->
              <div class="mb-6">
                <h3 class="text-sm font-semibold text-gray-900 uppercase tracking-wide mb-3">View Options</h3>
                <div class="bg-gray-100 rounded-lg p-1 flex flex-col space-y-1">
                  <button
                    id="list-view-btn"
                    class="px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 bg-white text-gray-900 shadow-sm text-left"
                    onclick="showListView()"
                  >
                    <span class="flex items-center">
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                      </svg>
                      List View
                    </span>
                  </button>
                  <button
                    id="grid-view-btn"
                    class="px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 text-gray-600 hover:text-gray-900 text-left"
                    onclick="showGridView()"
                  >
                    <span class="flex items-center">
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2z"
                        ></path>
                      </svg>
                      Grid View
                    </span>
                  </button>
                </div>
              </div>

              <!-- Quick Stats -->
              <div class="mb-6">
                <h3 class="text-sm font-semibold text-gray-900 uppercase tracking-wide mb-3">Quick Stats</h3>
                <div class="bg-gray-50 rounded-lg p-4">
                  <div class="text-2xl font-bold text-primary">{posts.length}</div>
                  <div class="text-sm text-muted">Total Publications</div>
                </div>
              </div>
            </>
          )}
        </div>
      </aside>

      <!-- Main Content Area -->
      <main class="flex-1 min-w-0">
        {
          !publicationsEnabled ? (
            <div class="text-center py-12">
              <div class="max-w-md mx-auto">
                <div class="mb-6">
                  <svg class="w-16 h-16 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                    />
                  </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Publications Coming Soon</h3>
                <p class="text-muted mb-4">
                  We're working on bringing you valuable insights and research. Our publications section will be
                  available soon.
                </p>
                <p class="text-sm text-gray-500">
                  In the meantime, feel free to explore our services or get in touch with us.
                </p>
                <div class="mt-6 space-x-4">
                  <a
                    href="/services"
                    class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
                  >
                    View Services
                  </a>
                  <a
                    href="/contact"
                    class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                  >
                    Contact Us
                  </a>
                </div>
              </div>
            </div>
          ) : loadingError ? (
            <SanityLoader error={loadingError} />
          ) : (
            <PublicationsLoader posts={posts} publicationsEnabled={publicationsEnabled}>
              <PostsList posts={posts} slot="list-view" />
              <PostsGrid posts={posts} slot="grid-view" />
            </PublicationsLoader>
          )
        }
      </main>
    </div>
  </section>

  <script>
    function showListView() {
      const listView = document.getElementById('list-view');
      const gridView = document.getElementById('grid-view');
      const listBtn = document.getElementById('list-view-btn');
      const gridBtn = document.getElementById('grid-view-btn');

      if (listView && gridView && listBtn && gridBtn) {
        listView.classList.remove('hidden');
        gridView.classList.add('hidden');

        listBtn.classList.add('bg-white', 'text-gray-900', 'shadow-sm');
        listBtn.classList.remove('text-gray-600', 'hover:text-gray-900');

        gridBtn.classList.remove('bg-white', 'text-gray-900', 'shadow-sm');
        gridBtn.classList.add('text-gray-600', 'hover:text-gray-900');
      }
    }

    function showGridView() {
      const listView = document.getElementById('list-view');
      const gridView = document.getElementById('grid-view');
      const listBtn = document.getElementById('list-view-btn');
      const gridBtn = document.getElementById('grid-view-btn');

      if (listView && gridView && listBtn && gridBtn) {
        listView.classList.add('hidden');
        gridView.classList.remove('hidden');

        gridBtn.classList.add('bg-white', 'text-gray-900', 'shadow-sm');
        gridBtn.classList.remove('text-gray-600', 'hover:text-gray-900');

        listBtn.classList.remove('bg-white', 'text-gray-900', 'shadow-sm');
        listBtn.classList.add('text-gray-600', 'hover:text-gray-900');
      }
    }

    // Make functions globally available
    declare global {
      interface Window {
        showListView: () => void;
        showGridView: () => void;
      }
    }

    window.showListView = showListView;
    window.showGridView = showGridView;
  </script>
</Layout>
