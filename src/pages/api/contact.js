export const prerender = false;

export const POST = async ({ request }) => {
  try {
    const data = await request.formData();

    const name = data.get('name');
    const email = data.get('email');
    const company = data.get('company');
    const subject = data.get('subject');
    const message = data.get('message');

    // Validate the data
    if (!name || !email || !message) {
      return new Response(
        JSON.stringify({
          message: 'Name, email, and message are required',
        }),
        { status: 400 }
      );
    }

    // Construct the email content
    const emailSubject = subject || `New contact form submission from ${name}`;
    const emailBody = `
      Name: ${name}
      Email: ${email}
      ${company ? `Company: ${company}` : ''}
      ${subject ? `Subject: ${subject}` : ''}

      Message:
      ${message}
    `;


    // Using Brevo (formerly Sendinblue) API
    const response = await fetch('https://api.brevo.com/v3/smtp/email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'api-key': `${import.meta.env.BREVO_API_KEY || process.env.BREVO_API_KEY || 'your-brevo-api-key'}`,
      },
      body: JSON.stringify({
        sender: {
          name: 'Gameometry',
          email: '<EMAIL>'
        },
        to: [
          {
            email: '<EMAIL>',
            name: 'Gameometry Team'
          }
        ],
        subject: emailSubject,
        textContent: emailBody,
        replyTo: {
          email: email,
          name: name
        },
        headers: {
          'X-Mailin-custom': 'contact_form:gameometry_website'
        }
      }),
    });

    // Check if the email was sent successfully
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Error sending email via Brevo:', errorData);
      throw new Error(`Failed to send email: ${response.status} ${response.statusText}`);
    }

    return new Response(
      JSON.stringify({
        message: 'Your message has been sent successfully!',
      }),
      { status: 200 }
    );
  } catch (error) {
    console.error('Error processing contact form:', error);

    return new Response(
      JSON.stringify({
        message: 'There was an error sending your message. Please try again later.',
      }),
      { status: 500 }
    );
  }
};
