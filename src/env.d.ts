// eslint-disable-next-line @typescript-eslint/triple-slash-reference
/// <reference path="../.astro/types.d.ts" />
/// <reference types="astro/client" />
/// <reference types="vite/client" />
/// <reference types="../vendor/integration/types.d.ts" />
/// <reference types="@sanity/astro/module" />

interface ImportMetaEnv {
    readonly PUBLIC_SANITY_PROJECT_ID: string;
    readonly PUBLIC_SANITY_DATASET: string;
    readonly SANITY_API_READ_TOKEN: string;
    readonly PUBLIC_SANITY_VISUAL_EDITING_ENABLED: string;
    readonly BREVO_API_KEY: string;
    readonly NODE_ENV: string;
    readonly PUBLIC_SHOW_PUBLICATIONS: string;
}

interface ImportMeta {
    readonly env: ImportMetaEnv;
}

// Global window extensions for loading functionality
declare global {
    interface Window {
        loadingBar?: {
            show: () => void;
            hide: () => void;
            complete: () => void;
            isVisible: boolean;
        };
        navigationLoader?: {
            show: () => void;
            hide: () => void;
            reset: () => void;
        };
        showListView?: () => void;
        showGridView?: () => void;
    }
}
