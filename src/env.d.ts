// eslint-disable-next-line @typescript-eslint/triple-slash-reference
/// <reference path="../.astro/types.d.ts" />
/// <reference types="astro/client" />
/// <reference types="vite/client" />
/// <reference types="../vendor/integration/types.d.ts" />
/// <reference types="@sanity/astro/module" />

interface ImportMetaEnv {
    readonly PUBLIC_SANITY_PROJECT_ID: string;
    readonly PUBLIC_SANITY_DATASET: string;
    readonly SANITY_API_READ_TOKEN: string;
    readonly PUBLIC_SANITY_VISUAL_EDITING_ENABLED: string;
    readonly BREVO_API_KEY: string;
    readonly NODE_ENV: string;
}

interface ImportMeta {
    readonly env: ImportMetaEnv;
}
