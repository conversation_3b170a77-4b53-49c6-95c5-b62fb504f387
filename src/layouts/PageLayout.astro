---
import Layout from '~/layouts/Layout.astro';
import Header from '~/components/widgets/Header.astro';
import Footer from '~/components/widgets/Footer.astro';
import { VisualEditing } from '@sanity/astro/visual-editing';

import { headerData, footerData } from '~/navigation';

import type { MetaData } from '~/types';

export interface Props {
  metadata?: MetaData;
}

const visualEditingEnabled = import.meta.env.PUBLIC_SANITY_VISUAL_EDITING_ENABLED == 'true';

const { metadata } = Astro.props;
---

<Layout metadata={metadata}>
  <div class="min-h-screen flex flex-col">
    <slot name="header">
      <Header {...headerData} isSticky showToggleTheme />
    </slot>
    <main class="flex-1">
      <slot />
      <VisualEditing enabled={visualEditingEnabled} zIndex={1000} />
    </main>
    <slot name="footer">
      <Footer {...footerData} />
    </slot>
  </div>
</Layout>
