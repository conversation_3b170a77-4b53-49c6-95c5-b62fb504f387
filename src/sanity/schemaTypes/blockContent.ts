// ./src/sanity/schemaTypes/blockContent.ts
import { defineType, defineArrayMember } from "sanity";

/**
 * Enhanced block content type with CKEditor-style capabilities
 * This provides comprehensive rich text editing features including:
 * - Multiple heading levels and text styles
 * - Advanced formatting options (underline, strikethrough, code, etc.)
 * - Lists (ordered, unordered, custom)
 * - Links with advanced options
 * - Media embeds (images, videos, code blocks)
 * - Custom components (callouts, tables, etc.)
 *
 * Usage:
 *  {
 *    name: 'content',
 *    title: 'Content',
 *    type: 'blockContent'
 *  }
 */

export const blockContentType = defineType({
  title: "Block Content",
  name: "blockContent",
  type: "array",
  of: [
    defineArrayMember({
      type: "block",
      // Enhanced styles for comprehensive text formatting
      styles: [
        { title: "Normal", value: "normal" },
        { title: "Heading 1", value: "h1" },
        { title: "Heading 2", value: "h2" },
        { title: "Heading 3", value: "h3" },
        { title: "Heading 4", value: "h4" },
        { title: "Heading 5", value: "h5" },
        { title: "Heading 6", value: "h6" },
        { title: "Quote", value: "blockquote" },
        { title: "Code Block", value: "code" },
        { title: "Lead Text", value: "lead" },
        { title: "Small Text", value: "small" },
      ],
      // Enhanced list types
      lists: [
        { title: "Bullet List", value: "bullet" },
        { title: "Numbered List", value: "number" },
        { title: "Checklist", value: "checklist" },
      ],
      // Comprehensive marks for inline formatting
      marks: {
        // Enhanced decorators for rich text formatting
        decorators: [
          { title: "Bold", value: "strong" },
          { title: "Italic", value: "em" },
          { title: "Underline", value: "underline" },
          { title: "Strike-through", value: "strike-through" },
          { title: "Code", value: "code" },
          { title: "Highlight", value: "highlight" },
          { title: "Superscript", value: "sup" },
          { title: "Subscript", value: "sub" },
          // Font size decorators
          { title: "Large Text", value: "large" },
          { title: "Small Text", value: "small" },
          { title: "Extra Large", value: "xl" },
          { title: "Extra Small", value: "xs" },
        ],
        // Enhanced annotations with advanced link options
        annotations: [
          {
            title: "Link",
            name: "link",
            type: "object",
            fields: [
              {
                title: "URL",
                name: "href",
                type: "url",
                validation: (Rule) => Rule.required(),
              },
              {
                title: "Link Text",
                name: "text",
                type: "string",
                description: "Optional custom link text",
              },
              {
                title: "Open in New Tab",
                name: "blank",
                type: "boolean",
                initialValue: false,
              },
              {
                title: "No Follow",
                name: "nofollow",
                type: "boolean",
                initialValue: false,
                description: "Add rel='nofollow' attribute",
              },
            ],
          },
          {
            title: "Internal Link",
            name: "internalLink",
            type: "object",
            fields: [
              {
                title: "Reference",
                name: "reference",
                type: "reference",
                to: [
                  { type: "post" },
                  // Add other document types as needed
                ],
              },
              {
                title: "Link Text",
                name: "text",
                type: "string",
                description: "Optional custom link text",
              },
            ],
          },
          {
            title: "Footnote",
            name: "footnote",
            type: "object",
            fields: [
              {
                title: "Footnote Text",
                name: "text",
                type: "text",
                rows: 2,
              },
            ],
          },
          {
            title: "Font Style",
            name: "fontStyle",
            type: "object",
            fields: [
              {
                title: "Font Family",
                name: "fontFamily",
                type: "string",
                options: {
                  list: [
                    { title: "Default", value: "default" },
                    { title: "Serif (Times)", value: "serif" },
                    { title: "Sans Serif (Arial)", value: "sans-serif" },
                    { title: "Monospace (Courier)", value: "monospace" },
                    { title: "Cursive", value: "cursive" },
                    { title: "Fantasy", value: "fantasy" },
                    // Custom fonts
                    { title: "Inter", value: "inter" },
                    { title: "Roboto", value: "roboto" },
                    { title: "Open Sans", value: "open-sans" },
                    { title: "Lato", value: "lato" },
                    { title: "Montserrat", value: "montserrat" },
                    { title: "Poppins", value: "poppins" },
                    { title: "Playfair Display", value: "playfair" },
                    { title: "Merriweather", value: "merriweather" },
                  ],
                },
                initialValue: "default",
              },
              {
                title: "Font Size",
                name: "fontSize",
                type: "string",
                options: {
                  list: [
                    { title: "Extra Small (12px)", value: "xs" },
                    { title: "Small (14px)", value: "sm" },
                    { title: "Base (16px)", value: "base" },
                    { title: "Large (18px)", value: "lg" },
                    { title: "Extra Large (20px)", value: "xl" },
                    { title: "2X Large (24px)", value: "2xl" },
                    { title: "3X Large (30px)", value: "3xl" },
                    { title: "4X Large (36px)", value: "4xl" },
                  ],
                },
                initialValue: "base",
              },
              {
                title: "Font Weight",
                name: "fontWeight",
                type: "string",
                options: {
                  list: [
                    { title: "Thin (100)", value: "thin" },
                    { title: "Light (300)", value: "light" },
                    { title: "Normal (400)", value: "normal" },
                    { title: "Medium (500)", value: "medium" },
                    { title: "Semibold (600)", value: "semibold" },
                    { title: "Bold (700)", value: "bold" },
                    { title: "Extra Bold (800)", value: "extrabold" },
                    { title: "Black (900)", value: "black" },
                  ],
                },
                initialValue: "normal",
              },
              {
                title: "Text Color",
                name: "color",
                type: "string",
                options: {
                  list: [
                    { title: "Default", value: "default" },
                    { title: "Primary", value: "primary" },
                    { title: "Secondary", value: "secondary" },
                    { title: "Accent", value: "accent" },
                    { title: "Muted", value: "muted" },
                    { title: "Success", value: "success" },
                    { title: "Warning", value: "warning" },
                    { title: "Error", value: "error" },
                    { title: "Info", value: "info" },
                    // Specific colors
                    { title: "Black", value: "black" },
                    { title: "White", value: "white" },
                    { title: "Gray", value: "gray" },
                    { title: "Red", value: "red" },
                    { title: "Blue", value: "blue" },
                    { title: "Green", value: "green" },
                    { title: "Yellow", value: "yellow" },
                    { title: "Purple", value: "purple" },
                    { title: "Pink", value: "pink" },
                    { title: "Indigo", value: "indigo" },
                  ],
                },
                initialValue: "default",
              },
              {
                title: "Background Color",
                name: "backgroundColor",
                type: "string",
                options: {
                  list: [
                    { title: "None", value: "none" },
                    { title: "Light Gray", value: "gray-light" },
                    { title: "Light Blue", value: "blue-light" },
                    { title: "Light Green", value: "green-light" },
                    { title: "Light Yellow", value: "yellow-light" },
                    { title: "Light Red", value: "red-light" },
                    { title: "Light Purple", value: "purple-light" },
                    { title: "Primary Light", value: "primary-light" },
                    { title: "Secondary Light", value: "secondary-light" },
                  ],
                },
                initialValue: "none",
              },
              {
                title: "Text Transform",
                name: "textTransform",
                type: "string",
                options: {
                  list: [
                    { title: "None", value: "none" },
                    { title: "Uppercase", value: "uppercase" },
                    { title: "Lowercase", value: "lowercase" },
                    { title: "Capitalize", value: "capitalize" },
                  ],
                },
                initialValue: "none",
              },
              {
                title: "Letter Spacing",
                name: "letterSpacing",
                type: "string",
                options: {
                  list: [
                    { title: "Normal", value: "normal" },
                    { title: "Tight", value: "tight" },
                    { title: "Wide", value: "wide" },
                    { title: "Wider", value: "wider" },
                    { title: "Widest", value: "widest" },
                  ],
                },
                initialValue: "normal",
              },
            ],
            preview: {
              select: {
                fontFamily: "fontFamily",
                fontSize: "fontSize",
                color: "color",
              },
              prepare({ fontFamily, fontSize, color }) {
                return {
                  title: "Font Style",
                  subtitle: `${fontFamily || 'default'} • ${fontSize || 'base'} • ${color || 'default'}`,
                };
              },
            },
          },
          {
            title: "Text Alignment",
            name: "textAlign",
            type: "object",
            fields: [
              {
                title: "Alignment",
                name: "align",
                type: "string",
                options: {
                  list: [
                    { title: "Left", value: "left" },
                    { title: "Center", value: "center" },
                    { title: "Right", value: "right" },
                    { title: "Justify", value: "justify" },
                  ],
                },
                initialValue: "left",
              },
            ],
            preview: {
              select: {
                align: "align",
              },
              prepare({ align }) {
                return {
                  title: "Text Alignment",
                  subtitle: align?.charAt(0).toUpperCase() + align?.slice(1),
                };
              },
            },
          },
        ],
      },
    }),

    // Enhanced Image Block with comprehensive options
    defineArrayMember({
      type: "image",
      title: "Image",
      options: { hotspot: true },
      fields: [
        {
          name: "alt",
          type: "string",
          title: "Alternative Text",
          description: "Important for accessibility and SEO",
          validation: (Rule) => Rule.required(),
        },
        {
          name: "caption",
          type: "string",
          title: "Caption",
          description: "Optional image caption",
        },
        {
          name: "attribution",
          type: "string",
          title: "Attribution",
          description: "Photo credit or source",
        },
        {
          name: "size",
          type: "string",
          title: "Size",
          options: {
            list: [
              { title: "Small", value: "small" },
              { title: "Medium", value: "medium" },
              { title: "Large", value: "large" },
              { title: "Full Width", value: "fullWidth" },
            ],
          },
          initialValue: "medium",
        },
        {
          name: "alignment",
          type: "string",
          title: "Alignment",
          options: {
            list: [
              { title: "Left", value: "left" },
              { title: "Center", value: "center" },
              { title: "Right", value: "right" },
            ],
          },
          initialValue: "center",
        },
      ],
    }),

    // Code Block Component
    defineArrayMember({
      type: "object",
      name: "codeBlock",
      title: "Code Block",
      fields: [
        {
          name: "language",
          type: "string",
          title: "Language",
          options: {
            list: [
              { title: "JavaScript", value: "javascript" },
              { title: "TypeScript", value: "typescript" },
              { title: "HTML", value: "html" },
              { title: "CSS", value: "css" },
              { title: "Python", value: "python" },
              { title: "Java", value: "java" },
              { title: "C++", value: "cpp" },
              { title: "JSON", value: "json" },
              { title: "Markdown", value: "markdown" },
              { title: "Shell", value: "shell" },
              { title: "SQL", value: "sql" },
              { title: "PHP", value: "php" },
              { title: "Ruby", value: "ruby" },
              { title: "Go", value: "go" },
              { title: "Rust", value: "rust" },
              { title: "Plain Text", value: "text" },
            ],
          },
          initialValue: "javascript",
        },
        {
          name: "code",
          type: "text",
          title: "Code",
          rows: 10,
          validation: (Rule) => Rule.required(),
        },
        {
          name: "filename",
          type: "string",
          title: "Filename",
          description: "Optional filename to display",
        },
        {
          name: "highlightLines",
          type: "string",
          title: "Highlight Lines",
          description: "Comma-separated line numbers to highlight (e.g., 1,3-5,8)",
        },
      ],
      preview: {
        select: {
          title: "filename",
          subtitle: "language",
          code: "code",
        },
        prepare({ title, subtitle, code }) {
          return {
            title: title || "Code Block",
            subtitle: `${subtitle} • ${code?.split('\n').length || 0} lines`,
          };
        },
      },
    }),

    // Callout/Alert Component
    defineArrayMember({
      type: "object",
      name: "callout",
      title: "Callout",
      fields: [
        {
          name: "type",
          type: "string",
          title: "Type",
          options: {
            list: [
              { title: "Info", value: "info" },
              { title: "Warning", value: "warning" },
              { title: "Error", value: "error" },
              { title: "Success", value: "success" },
              { title: "Note", value: "note" },
              { title: "Tip", value: "tip" },
            ],
          },
          initialValue: "info",
        },
        {
          name: "title",
          type: "string",
          title: "Title",
          description: "Optional callout title",
        },
        {
          name: "content",
          type: "array",
          title: "Content",
          of: [
            {
              type: "block",
              styles: [{ title: "Normal", value: "normal" }],
              marks: {
                decorators: [
                  { title: "Bold", value: "strong" },
                  { title: "Italic", value: "em" },
                  { title: "Code", value: "code" },
                ],
                annotations: [
                  {
                    title: "Link",
                    name: "link",
                    type: "object",
                    fields: [
                      {
                        title: "URL",
                        name: "href",
                        type: "url",
                      },
                    ],
                  },
                ],
              },
            },
          ],
        },
      ],
      preview: {
        select: {
          title: "title",
          type: "type",
        },
        prepare({ title, type }) {
          return {
            title: title || "Callout",
            subtitle: type?.charAt(0).toUpperCase() + type?.slice(1),
          };
        },
      },
    }),

    // Table Component
    defineArrayMember({
      type: "object",
      name: "table",
      title: "Table",
      fields: [
        {
          name: "caption",
          type: "string",
          title: "Caption",
          description: "Optional table caption",
        },
        {
          name: "rows",
          type: "array",
          title: "Rows",
          of: [
            {
              type: "object",
              name: "row",
              fields: [
                {
                  name: "cells",
                  type: "array",
                  title: "Cells",
                  of: [
                    {
                      type: "string",
                      name: "cell",
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          name: "hasHeader",
          type: "boolean",
          title: "First Row is Header",
          initialValue: true,
        },
      ],
      preview: {
        select: {
          caption: "caption",
          rows: "rows",
        },
        prepare({ caption, rows }) {
          const rowCount = rows?.length || 0;
          const colCount = rows?.[0]?.cells?.length || 0;
          return {
            title: caption || "Table",
            subtitle: `${rowCount} rows × ${colCount} columns`,
          };
        },
      },
    }),

    // Video Embed Component
    defineArrayMember({
      type: "object",
      name: "videoEmbed",
      title: "Video Embed",
      fields: [
        {
          name: "url",
          type: "url",
          title: "Video URL",
          description: "YouTube, Vimeo, or direct video URL",
          validation: (Rule) => Rule.required(),
        },
        {
          name: "title",
          type: "string",
          title: "Video Title",
          description: "Accessible title for the video",
        },
        {
          name: "aspectRatio",
          type: "string",
          title: "Aspect Ratio",
          options: {
            list: [
              { title: "16:9 (Widescreen)", value: "16:9" },
              { title: "4:3 (Standard)", value: "4:3" },
              { title: "1:1 (Square)", value: "1:1" },
              { title: "9:16 (Vertical)", value: "9:16" },
            ],
          },
          initialValue: "16:9",
        },
      ],
      preview: {
        select: {
          title: "title",
          url: "url",
        },
        prepare({ title, url }) {
          return {
            title: title || "Video Embed",
            subtitle: url,
          };
        },
      },
    }),

    // Divider/Separator Component
    defineArrayMember({
      type: "object",
      name: "divider",
      title: "Divider",
      fields: [
        {
          name: "style",
          type: "string",
          title: "Style",
          options: {
            list: [
              { title: "Line", value: "line" },
              { title: "Dots", value: "dots" },
              { title: "Stars", value: "stars" },
              { title: "Spacing", value: "spacing" },
            ],
          },
          initialValue: "line",
        },
        {
          name: "size",
          type: "string",
          title: "Size",
          options: {
            list: [
              { title: "Small", value: "small" },
              { title: "Medium", value: "medium" },
              { title: "Large", value: "large" },
            ],
          },
          initialValue: "medium",
        },
      ],
      preview: {
        prepare() {
          return {
            title: "Divider",
            subtitle: "Section separator",
          };
        },
      },
    }),
  ],
});
