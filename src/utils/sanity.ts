import type { <PERSON>ity<PERSON><PERSON>, <PERSON>ityAuthor, SanityCategory, Post, Taxonomy } from '~/types';
import { urlForImage } from '~/sanity/lib/url-for-image';
import { hasName, hasTitle } from './type-guards';

/**
 * Type guard to check if a value is a populated SanityAuthor
 */
function isPopulatedAuthor(author: any): author is Sanity<PERSON>uth<PERSON> {
  return hasName(author);
}

/**
 * Type guard to check if a value is a populated SanityCategory
 */
function isPopulatedCategory(category: any): category is SanityCategory {
  return hasTitle(category);
}

/**
 * Converts a SanityPost to the standard Post interface used throughout the application
 */
export function sanityPostToPost(sanityPost: SanityPost): Post {
  // Convert author reference to string
  const author = isPopulatedAuthor(sanityPost.author)
    ? sanityPost.author.name
    : undefined;

  // Convert categories to taxonomy array
  const categories = sanityPost.categories?.map((cat) => {
    if (isPopulatedCategory(cat)) {
      return {
        slug: cat.title.toLowerCase().replace(/\s+/g, '-'),
        title: cat.title,
      } as Taxonomy;
    }
    return null;
  }).filter(Boolean) as Taxonomy[] || [];

  // Get the first category as the main category
  const category = categories.length > 0 ? categories[0] : undefined;

  // Convert main image to image URL
  const image = sanityPost.mainImage
    ? urlForImage(sanityPost.mainImage)?.url()
    : undefined;

  // Convert publishedAt to Date
  const publishDate = sanityPost.publishedAt
    ? new Date(sanityPost.publishedAt)
    : new Date();

  // Generate slug from Sanity slug
  const slug = sanityPost.slug?.current || sanityPost.title.toLowerCase().replace(/\s+/g, '-');

  // Generate permalink
  const permalink = `/${slug}`;

  return {
    id: sanityPost._id,
    slug,
    permalink,
    publishDate,
    title: sanityPost.title,
    image,
    category,
    tags: categories.slice(1), // Use remaining categories as tags
    author,
    draft: false,
    // Note: excerpt and content would need to be generated from the body field
    // This would require additional processing of the PortableText content
  };
}

/**
 * Converts an array of SanityPost objects to Post objects
 */
export function sanityPostsToPosts(sanityPosts: SanityPost[]): Post[] {
  if (!Array.isArray(sanityPosts)) {
    console.warn('sanityPostsToPosts: Expected array but received:', typeof sanityPosts);
    return [];
  }

  return sanityPosts
    .filter((post) => post && typeof post === 'object' && post._id && post.title)
    .map((post) => {
      try {
        return sanityPostToPost(post);
      } catch (error) {
        console.error('Error converting Sanity post to Post:', error, post);
        return null;
      }
    })
    .filter(Boolean) as Post[];
}

/**
 * GROQ query to fetch posts with populated references
 */
export const POSTS_QUERY = `
  *[_type == "post"] | order(publishedAt desc) {
    _id,
    _type,
    _createdAt,
    _updatedAt,
    _rev,
    title,
    slug,
    "author": author->{
      _id,
      _type,
      name,
      slug,
      image,
      bio
    },
    mainImage,
    "categories": categories[]->{
      _id,
      _type,
      title,
      description
    },
    publishedAt,
    body
  }
`;

/**
 * GROQ query to fetch a single post by slug
 */
export const POST_BY_SLUG_QUERY = `
  *[_type == "post" && slug.current == $slug][0] {
    _id,
    _type,
    _createdAt,
    _updatedAt,
    _rev,
    title,
    slug,
    "author": author->{
      _id,
      _type,
      name,
      slug,
      image,
      bio
    },
    mainImage,
    "categories": categories[]->{
      _id,
      _type,
      title,
      description
    },
    publishedAt,
    body
  }
`;

/**
 * GROQ query to fetch posts by category
 */
export const POSTS_BY_CATEGORY_QUERY = `
  *[_type == "post" && $categoryId in categories[]._ref] | order(publishedAt desc) {
    _id,
    _type,
    _createdAt,
    _updatedAt,
    _rev,
    title,
    slug,
    "author": author->{
      _id,
      _type,
      name,
      slug,
      image,
      bio
    },
    mainImage,
    "categories": categories[]->{
      _id,
      _type,
      title,
      description
    },
    publishedAt,
    body
  }
`;
