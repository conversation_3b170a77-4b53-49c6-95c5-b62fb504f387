import type { <PERSON>ity<PERSON><PERSON>, <PERSON>ityAuthor, SanityCategory, Post, Taxonomy } from '~/types';
import { urlForImage } from '~/sanity/lib/url-for-image';

/**
 * Converts a SanityPost to the standard Post interface used throughout the application
 */
export function sanityPostToPost(sanityPost: SanityPost): Post {
  // Convert author reference to string
  const author = typeof sanityPost.author === 'object' && 'name' in sanityPost.author 
    ? (sanityPost.author as SanityAuthor).name 
    : undefined;

  // Convert categories to taxonomy array
  const categories = sanityPost.categories?.map((cat) => {
    if (typeof cat === 'object' && 'title' in cat) {
      const category = cat as SanityCategory;
      return {
        slug: category.title.toLowerCase().replace(/\s+/g, '-'),
        title: category.title,
      } as Taxonomy;
    }
    return null;
  }).filter(Boolean) as Taxonomy[] || [];

  // Get the first category as the main category
  const category = categories.length > 0 ? categories[0] : undefined;

  // Convert main image to image URL
  const image = sanityPost.mainImage 
    ? urlForImage(sanityPost.mainImage)?.url() 
    : undefined;

  // Convert publishedAt to Date
  const publishDate = sanityPost.publishedAt 
    ? new Date(sanityPost.publishedAt) 
    : new Date();

  // Generate slug from Sanity slug
  const slug = sanityPost.slug?.current || sanityPost.title.toLowerCase().replace(/\s+/g, '-');

  // Generate permalink
  const permalink = `/${slug}`;

  return {
    id: sanityPost._id,
    slug,
    permalink,
    publishDate,
    title: sanityPost.title,
    image,
    category,
    tags: categories.slice(1), // Use remaining categories as tags
    author,
    draft: false,
    // Note: excerpt and content would need to be generated from the body field
    // This would require additional processing of the PortableText content
  };
}

/**
 * Converts an array of SanityPost objects to Post objects
 */
export function sanityPostsToPosts(sanityPosts: SanityPost[]): Post[] {
  return sanityPosts.map(sanityPostToPost);
}

/**
 * GROQ query to fetch posts with populated references
 */
export const POSTS_QUERY = `
  *[_type == "post"] | order(publishedAt desc) {
    _id,
    _type,
    _createdAt,
    _updatedAt,
    _rev,
    title,
    slug,
    "author": author->{
      _id,
      _type,
      name,
      slug,
      image,
      bio
    },
    mainImage,
    "categories": categories[]->{
      _id,
      _type,
      title,
      description
    },
    publishedAt,
    body
  }
`;

/**
 * GROQ query to fetch a single post by slug
 */
export const POST_BY_SLUG_QUERY = `
  *[_type == "post" && slug.current == $slug][0] {
    _id,
    _type,
    _createdAt,
    _updatedAt,
    _rev,
    title,
    slug,
    "author": author->{
      _id,
      _type,
      name,
      slug,
      image,
      bio
    },
    mainImage,
    "categories": categories[]->{
      _id,
      _type,
      title,
      description
    },
    publishedAt,
    body
  }
`;

/**
 * GROQ query to fetch posts by category
 */
export const POSTS_BY_CATEGORY_QUERY = `
  *[_type == "post" && $categoryId in categories[]._ref] | order(publishedAt desc) {
    _id,
    _type,
    _createdAt,
    _updatedAt,
    _rev,
    title,
    slug,
    "author": author->{
      _id,
      _type,
      name,
      slug,
      image,
      bio
    },
    mainImage,
    "categories": categories[]->{
      _id,
      _type,
      title,
      description
    },
    publishedAt,
    body
  }
`;
