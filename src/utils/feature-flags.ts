/**
 * Feature flag utilities for controlling application features
 */

/**
 * Checks if publications feature is enabled
 * @returns boolean indicating if publications should be shown
 */
export function isPublicationsEnabled(): boolean {
  const flag = import.meta.env.PUBLIC_SHOW_PUBLICATIONS;
  
  // Default to false if not set or invalid
  if (!flag || typeof flag !== 'string') {
    return false;
  }
  
  // Parse string to boolean
  return flag.toLowerCase() === 'true';
}

/**
 * Checks if Sanity visual editing is enabled
 * @returns boolean indicating if visual editing should be enabled
 */
export function isVisualEditingEnabled(): boolean {
  const flag = import.meta.env.PUBLIC_SANITY_VISUAL_EDITING_ENABLED;
  
  // Default to false if not set or invalid
  if (!flag || typeof flag !== 'string') {
    return false;
  }
  
  // Parse string to boolean
  return flag.toLowerCase() === 'true';
}

/**
 * Checks if we're in development mode
 * @returns boolean indicating if in development
 */
export function isDevelopment(): boolean {
  const env = import.meta.env.NODE_ENV;
  return env === 'development' || env === 'local';
}

/**
 * Checks if we're in production mode
 * @returns boolean indicating if in production
 */
export function isProduction(): boolean {
  const env = import.meta.env.NODE_ENV;
  return env === 'production';
}

/**
 * Gets the current environment name
 * @returns string representing the current environment
 */
export function getEnvironment(): string {
  return import.meta.env.NODE_ENV || 'unknown';
}

/**
 * Feature flag configuration object
 */
export const featureFlags = {
  publications: isPublicationsEnabled(),
  visualEditing: isVisualEditingEnabled(),
  development: isDevelopment(),
  production: isProduction(),
  environment: getEnvironment(),
} as const;

/**
 * Logs current feature flag status (useful for debugging)
 */
export function logFeatureFlags(): void {
  if (isDevelopment()) {
    console.log('🚩 Feature Flags:', featureFlags);
  }
}
