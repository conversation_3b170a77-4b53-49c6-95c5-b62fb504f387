/**
 * Type guard utilities for safe type checking
 * These functions help avoid "Cannot use 'in' operator" errors
 */

/**
 * Checks if an object has a 'name' property of type string
 */
export function hasName(obj: any): obj is { name: string } {
  return obj && typeof obj === 'object' && obj !== null && typeof obj.name === 'string';
}

/**
 * Checks if an object has a 'title' property of type string
 */
export function hasTitle(obj: any): obj is { title: string } {
  return obj && typeof obj === 'object' && obj !== null && typeof obj.title === 'string';
}

/**
 * Checks if an object has an '_id' property of type string
 */
export function hasId(obj: any): obj is { _id: string } {
  return obj && typeof obj === 'object' && obj !== null && typeof obj._id === 'string';
}

/**
 * Checks if an object has a 'slug' property with a 'current' field
 */
export function hasSlug(obj: any): obj is { slug: { current: string } } {
  return obj && 
    typeof obj === 'object' && 
    obj !== null && 
    obj.slug &&
    typeof obj.slug === 'object' &&
    obj.slug !== null &&
    typeof obj.slug.current === 'string';
}

/**
 * Checks if an object has a 'publishedAt' property of type string
 */
export function hasPublishedAt(obj: any): obj is { publishedAt: string } {
  return obj && typeof obj === 'object' && obj !== null && typeof obj.publishedAt === 'string';
}

/**
 * Checks if an object has a 'body' property (for PortableText content)
 */
export function hasBody(obj: any): obj is { body: any[] } {
  return obj && 
    typeof obj === 'object' && 
    obj !== null && 
    Array.isArray(obj.body);
}

/**
 * Checks if an object has a 'mainImage' property
 */
export function hasMainImage(obj: any): obj is { mainImage: any } {
  return obj && 
    typeof obj === 'object' && 
    obj !== null && 
    obj.mainImage &&
    typeof obj.mainImage === 'object';
}

/**
 * Checks if an object has a 'categories' property that is an array
 */
export function hasCategories(obj: any): obj is { categories: any[] } {
  return obj && 
    typeof obj === 'object' && 
    obj !== null && 
    Array.isArray(obj.categories);
}

/**
 * Checks if an object has an 'author' property
 */
export function hasAuthor(obj: any): obj is { author: any } {
  return obj && 
    typeof obj === 'object' && 
    obj !== null && 
    obj.author;
}

/**
 * Checks if a value is a non-empty string
 */
export function isNonEmptyString(value: any): value is string {
  return typeof value === 'string' && value.length > 0;
}

/**
 * Checks if a value is a valid date string or Date object
 */
export function isValidDate(value: any): value is string | Date {
  if (typeof value === 'string') {
    return !isNaN(Date.parse(value));
  }
  return value instanceof Date && !isNaN(value.getTime());
}

/**
 * Safely gets a nested property from an object
 */
export function safeGet<T>(obj: any, path: string, defaultValue?: T): T | undefined {
  try {
    const keys = path.split('.');
    let current = obj;
    
    for (const key of keys) {
      if (current && typeof current === 'object' && current !== null && key in current) {
        current = current[key];
      } else {
        return defaultValue;
      }
    }
    
    return current;
  } catch {
    return defaultValue;
  }
}
