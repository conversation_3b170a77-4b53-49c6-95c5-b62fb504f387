import { loadQuery } from '~/sanity/lib/load-query';
import type { SanityPost } from '~/types';
import { isPublicationsEnabled } from './feature-flags';

// GROQ query to fetch all published posts for sitemap
export const SITEMAP_POSTS_QUERY = `
  *[_type == "post" && defined(slug.current) && defined(publishedAt)] | order(publishedAt desc) {
    _id,
    _updatedAt,
    title,
    slug,
    publishedAt
  }
`;

// GROQ query to fetch other content types for sitemap (if you have them)
export const SITEMAP_PAGES_QUERY = `
  *[_type == "page" && defined(slug.current)] | order(_updatedAt desc) {
    _id,
    _updatedAt,
    title,
    slug
  }
`;

export interface SitemapUrl {
  loc: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}

/**
 * Get the base URL for the site
 */
export function getBaseUrl(): string {
  // Check for environment variables first
  if (import.meta.env.PUBLIC_SITE_URL) {
    return import.meta.env.PUBLIC_SITE_URL;
  }
  
  // Fallback to common deployment URLs
  if (import.meta.env.VERCEL_URL) {
    return `https://${import.meta.env.VERCEL_URL}`;
  }
  
  if (import.meta.env.NETLIFY_URL) {
    return import.meta.env.NETLIFY_URL;
  }
  
  // Development fallback
  return 'http://localhost:4321';
}

/**
 * Generate static page URLs for sitemap
 */
export function getStaticPages(): SitemapUrl[] {
  const baseUrl = getBaseUrl();
  
  const staticPages: SitemapUrl[] = [
    {
      loc: `${baseUrl}/`,
      changefreq: 'weekly',
      priority: 1.0,
    },
    {
      loc: `${baseUrl}/about`,
      changefreq: 'monthly',
      priority: 0.8,
    },
    {
      loc: `${baseUrl}/services`,
      changefreq: 'monthly',
      priority: 0.9,
    },
    {
      loc: `${baseUrl}/contact`,
      changefreq: 'monthly',
      priority: 0.7,
    },
  ];

  // Add publications page if enabled
  if (isPublicationsEnabled()) {
    staticPages.push({
      loc: `${baseUrl}/publications`,
      changefreq: 'daily',
      priority: 0.8,
    });
  }

  return staticPages;
}

/**
 * Fetch and generate publication URLs from Sanity
 */
export async function getPublicationUrls(): Promise<SitemapUrl[]> {
  if (!isPublicationsEnabled()) {
    return [];
  }

  try {
    const { data: posts } = await loadQuery<SanityPost[]>({
      query: SITEMAP_POSTS_QUERY,
    });

    if (!posts || !Array.isArray(posts)) {
      return [];
    }

    const baseUrl = getBaseUrl();
    
    return posts
      .filter(post => post.slug?.current)
      .map(post => ({
        loc: `${baseUrl}/publications/${post.slug!.current}`,
        lastmod: post._updatedAt || post.publishedAt,
        changefreq: 'weekly' as const,
        priority: 0.6,
      }));
  } catch (error) {
    console.error('Error fetching publication URLs for sitemap:', error);
    return [];
  }
}

/**
 * Fetch and generate other page URLs from Sanity (if you have a page content type)
 */
export async function getPageUrls(): Promise<SitemapUrl[]> {
  try {
    // Only fetch if you have a 'page' content type in Sanity
    // Uncomment and modify if you have custom pages
    /*
    const { data: pages } = await loadQuery<any[]>({
      query: SITEMAP_PAGES_QUERY,
    });

    if (!pages || !Array.isArray(pages)) {
      return [];
    }

    const baseUrl = getBaseUrl();
    
    return pages
      .filter(page => page.slug?.current)
      .map(page => ({
        loc: `${baseUrl}/${page.slug!.current}`,
        lastmod: page._updatedAt,
        changefreq: 'monthly' as const,
        priority: 0.5,
      }));
    */
    
    return [];
  } catch (error) {
    console.error('Error fetching page URLs for sitemap:', error);
    return [];
  }
}

/**
 * Generate complete sitemap URLs
 */
export async function generateSitemapUrls(): Promise<SitemapUrl[]> {
  const [staticPages, publicationUrls, pageUrls] = await Promise.all([
    getStaticPages(),
    getPublicationUrls(),
    getPageUrls(),
  ]);

  return [
    ...staticPages,
    ...publicationUrls,
    ...pageUrls,
  ].sort((a, b) => {
    // Sort by priority (highest first), then by URL
    if (a.priority !== b.priority) {
      return (b.priority || 0) - (a.priority || 0);
    }
    return a.loc.localeCompare(b.loc);
  });
}

/**
 * Generate XML sitemap content
 */
export function generateSitemapXml(urls: SitemapUrl[]): string {
  const xmlUrls = urls.map(url => {
    let urlXml = `    <url>\n      <loc>${escapeXml(url.loc)}</loc>`;
    
    if (url.lastmod) {
      const lastmod = new Date(url.lastmod).toISOString().split('T')[0];
      urlXml += `\n      <lastmod>${lastmod}</lastmod>`;
    }
    
    if (url.changefreq) {
      urlXml += `\n      <changefreq>${url.changefreq}</changefreq>`;
    }
    
    if (url.priority !== undefined) {
      urlXml += `\n      <priority>${url.priority.toFixed(1)}</priority>`;
    }
    
    urlXml += '\n    </url>';
    return urlXml;
  }).join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${xmlUrls}
</urlset>`;
}

/**
 * Escape XML special characters
 */
function escapeXml(unsafe: string): string {
  return unsafe.replace(/[<>&'"]/g, (c) => {
    switch (c) {
      case '<': return '&lt;';
      case '>': return '&gt;';
      case '&': return '&amp;';
      case "'": return '&apos;';
      case '"': return '&quot;';
      default: return c;
    }
  });
}

/**
 * Generate robots.txt content
 */
export function generateRobotsTxt(): string {
  const baseUrl = getBaseUrl();
  
  return `User-agent: *
Allow: /

# Sitemap
Sitemap: ${baseUrl}/sitemap.xml

# Disallow admin and API routes
Disallow: /admin/
Disallow: /api/
Disallow: /studio/
Disallow: /_astro/

# Allow specific important pages
Allow: /
Allow: /about
Allow: /services
Allow: /contact
${isPublicationsEnabled() ? 'Allow: /publications' : ''}`;
}
