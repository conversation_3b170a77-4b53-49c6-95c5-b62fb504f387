---
import { SITE } from 'gameometry:config';
import { getHomePermalink } from '~/utils/permalinks';

export interface Props {
  footNote?: string;
  theme?: string;
}

const { footNote = '', theme = 'light' } = Astro.props;
---

<footer class:list={[{ dark: theme === 'dark' }, 'relative border-t border-gray-200 dark:border-slate-800 not-prose']}>
  <div class="dark:bg-dark absolute inset-0 pointer-events-none" aria-hidden="true"></div>
  <div
    class="relative max-w-7xl mx-auto px-4 sm:px-6 dark:text-slate-300 intersect-once intersect-quarter intercept-no-queue motion-safe:md:opacity-0 motion-safe:md:intersect:animate-fade"
  >
    <div class="flex items-center justify-between py-4">
      <div class="text-sm dark:text-muted">
        <Fragment set:html={footNote} />
      </div>
    </div>
  </div>
</footer>
