---
import TeamMember from './TeamMember.astro';
import type { ImageMetadata } from 'astro';

interface Props {
  title?: string;
  subtitle?: string;
  highlight?: string;
  members: Array<{
    name: string;
    title: string;
    description: string;
    image: string | ImageMetadata;
    socialLinks?: {
      twitter?: string;
      linkedin?: string;
      github?: string;
    };
  }>;
}

const { title = 'Our Team', highlight, members = [] } = Astro.props;
---

<section class="py-16 bg-[#F2F2F2]">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      {highlight && <p class="text-base font-semibold uppercase tracking-wide text-primary">{highlight}</p>}
      {title && <h2 class="text-3xl md:text-4xl font-bold mb-4">{title}</h2>}
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-8">
      {
        members.map((member) => (
          <TeamMember
            name={member.name}
            title={member.title}
            description={member.description}
            image={member.image}
            socialLinks={member.socialLinks}
          />
        ))
      }
    </div>
  </div>
</section>
