---
import type { ImageMetadata } from 'astro';

interface Props {
  name: string;
  title: string;
  description: string;
  image: string | ImageMetadata;
  socialLinks?: {
    twitter?: string;
    linkedin?: string;
    github?: string;
  };
}

const { name, title, description, image } = Astro.props;
---

<div class="team-member group">
  <div class="relative overflow-hidden rounded-xl mb-4 aspect-square">
    <img
      src={typeof image === 'string' ? image : image.src}
      alt={name}
      class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
    />
  </div>
  <h3 class="text-xl font-bold">{name}</h3>
  <p class="text-primary font-medium mb-2">{title}</p>
  <p class="text-muted" set:html={description} />
</div>
