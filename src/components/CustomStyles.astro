---
import '@fontsource-variable/inter';

// 'DM Sans'
// Nunito
// Dosis
// Outfit
// Roboto
// Literata
// 'IBM Plex Sans'
// Karla
// Poppins
// 'Fira Sans'
// 'Libre Franklin'
// Inconsolata
// Raleway
// Oswald
// 'Space Grotesk'
// Urbanist
---

<style is:inline>
  :root {
    --aw-font-sans: 'Inter Variable';
    --aw-font-serif: 'Inter Variable';
    --aw-font-heading: 'Inter Variable';

    /* Professional business-focused colors */
    --aw-color-primary: rgb(0, 83, 156); /* Deep blue */
    --aw-color-secondary: rgb(64, 116, 161); /* Medium blue */
    --aw-color-accent: rgb(226, 108, 10); /* Orange accent */

    /* Theme-specific colors */
    --aw-color-videogame: rgb(64, 116, 161); /* Medium blue */
    --aw-color-casino: rgb(0, 83, 156); /* Deep blue */
    --aw-color-geometry: rgb(100, 151, 177); /* Light blue */
    --aw-color-consultancy: rgb(80, 80, 80); /* Dark gray */
    --aw-color-diversity: rgb(226, 108, 10); /* Orange */

    --aw-color-text-heading: rgb(0 0 0);
    --aw-color-text-default: rgb(16 16 16);
    --aw-color-text-muted: rgb(16 16 16 / 66%);
    --aw-color-bg-page: #f2f2f2;

    --aw-color-bg-page-dark: rgb(18, 18, 36); /* Richer dark background */

    ::selection {
      background-color: var(--aw-color-accent);
      color: white;
    }
  }

  .dark {
    --aw-font-sans: 'Inter Variable';
    --aw-font-serif: 'Inter Variable';
    --aw-font-heading: 'Inter Variable';

    /* Professional business-focused colors for dark mode */
    --aw-color-primary: rgb(41, 128, 185); /* Brighter blue */
    --aw-color-secondary: rgb(84, 153, 199); /* Medium blue */
    --aw-color-accent: rgb(243, 156, 18); /* Brighter orange */

    /* Theme-specific colors for dark mode - slightly brighter */
    --aw-color-videogame: rgb(84, 153, 199); /* Medium blue */
    --aw-color-casino: rgb(41, 128, 185); /* Brighter blue */
    --aw-color-geometry: rgb(133, 193, 233); /* Light blue */
    --aw-color-consultancy: rgb(149, 165, 166); /* Light gray */
    --aw-color-diversity: rgb(243, 156, 18); /* Brighter orange */

    --aw-color-text-heading: rgb(247, 248, 248);
    --aw-color-text-default: rgb(229 236 246);
    --aw-color-text-muted: rgb(229 236 246 / 66%);
    --aw-color-bg-page: rgb(18, 18, 36); /* Richer dark background */

    ::selection {
      background-color: var(--aw-color-accent);
      color: rgb(18, 18, 36);
    }
  }
</style>
