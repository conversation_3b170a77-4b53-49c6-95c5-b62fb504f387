---
import type { HTMLTag } from 'astro/types';
import type { Widget } from '~/types';
import { twMerge } from 'tailwind-merge';
import Background from './Background.astro';

export interface Props extends Widget {
  containerClass?: string;
  ['as']?: HTMLTag;
}

const { id, isDark = false, containerClass = '', bg, as = 'section' } = Astro.props;

const WrapperTag = as;
---

<WrapperTag class="relative not-prose scroll-mt-[72px]" {...id ? { id } : {}}>
  <div class="absolute inset-0 pointer-events-none -z-[1]" aria-hidden="true">
    <slot name="bg">
      {bg ? <Fragment set:html={bg} /> : <Background isDark={isDark} />}
    </slot>
  </div>
  <div
    class:list={[
      twMerge(
        'relative mx-auto max-w-7xl px-4 md:px-6 py-12 md:py-16 lg:py-20 text-default intersect-once intersect-quarter intercept-no-queue motion-safe:md:opacity-0 motion-safe:md:intersect:animate-fade',
        containerClass
      ),
      { dark: isDark },
    ]}
  >
    <slot />
  </div>
</WrapperTag>
