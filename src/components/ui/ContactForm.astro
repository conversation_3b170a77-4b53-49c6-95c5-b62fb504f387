---
const { button = 'Send Message' } = Astro.props;
---

<form action="/api/contact" method="POST" id="contact-form" class="space-y-4" onsubmit="return false;">
  <!-- Name Field -->
  <div>
    <input
      type="text"
      name="name"
      id="name"
      placeholder="Your Name"
      required
      class="py-1.5 px-2 block w-full text-sm rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-slate-900"
    />
    <div class="text-red-500 text-xs mt-1 hidden" id="name-error"></div>
  </div>

  <!-- Email Field -->
  <div>
    <input
      type="email"
      name="email"
      id="email"
      placeholder="Your Email"
      required
      class="py-1.5 px-2 block w-full text-sm rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-slate-900"
    />
    <div class="text-red-500 text-xs mt-1 hidden" id="email-error"></div>
  </div>

  <!-- Company Field (Optional) -->
  <div>
    <input
      type="text"
      name="company"
      id="company"
      placeholder="Your Company"
      class="py-1.5 px-2 block w-full text-sm rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-slate-900"
    />
  </div>

  <!-- Subject Field -->
  <div>
    <input
      type="text"
      name="subject"
      id="subject"
      placeholder="Subject"
      required
      class="py-1.5 px-2 block w-full text-sm rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-slate-900"
    />
    <div class="text-red-500 text-xs mt-1 hidden" id="subject-error"></div>
  </div>

  <!-- Message Field -->
  <div>
    <textarea
      name="message"
      id="message"
      placeholder="Your Message"
      rows="4"
      required
      class="py-1.5 px-2 block w-full text-sm rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-slate-900"
    ></textarea>
    <div class="text-red-500 text-xs mt-1 hidden" id="message-error"></div>
  </div>

  <!-- Submit Button -->
  <div class="flex justify-center sm:justify-start">
    <button type="submit" class="btn btn-primary px-8 py-2">
      {button}
    </button>
  </div>

  <!-- Form Status Messages -->
  <div id="form-status" class="mt-4 text-center hidden">
    <p id="success-message" class="text-green-600 dark:text-green-400 hidden">
      Your message has been sent successfully!
    </p>
    <p id="error-message" class="text-red-600 dark:text-red-400 hidden">
      There was an error sending your message. Please try again.
    </p>
  </div>
</form>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('contact-form') as HTMLFormElement;
    const formStatus = document.getElementById('form-status');
    const successMessage = document.getElementById('success-message');
    const errorMessage = document.getElementById('error-message');

    // Function to validate email format
    const isValidEmail = (email: string) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    };

    // Function to show validation error
    const showError = (fieldName: string, message: string) => {
      const errorElement = document.getElementById(`${fieldName}-error`);
      if (errorElement) {
        errorElement.textContent = message;
        errorElement.classList.remove('hidden');

        // Add red border to the input field
        const inputField = document.getElementById(fieldName);
        if (inputField) {
          inputField.classList.add('border-red-500');
        }
      }
    };

    // Function to clear validation errors
    const clearErrors = () => {
      const errorElements = document.querySelectorAll('[id$="-error"]');
      errorElements.forEach((element) => {
        element.textContent = '';
        element.classList.add('hidden');
      });

      // Remove red borders
      const inputFields = form.querySelectorAll('input, textarea');
      inputFields.forEach((field) => {
        field.classList.remove('border-red-500');
      });
    };

    // Add input validation on blur
    const inputFields = form.querySelectorAll('input, textarea');
    inputFields.forEach((field) => {
      field.addEventListener('blur', () => {
        const fieldName = field.getAttribute('name');
        const fieldValue = (field as HTMLInputElement | HTMLTextAreaElement).value.trim();

        // Skip validation for company field as it's optional or if fieldName is null
        if (!fieldName || fieldName === 'company') return;

        if (!fieldValue) {
          showError(fieldName, `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`);
        } else if (fieldName === 'email' && !isValidEmail(fieldValue)) {
          showError('email', 'Please enter a valid email address');
        } else {
          // Clear error if field is valid
          const errorElement = document.getElementById(`${fieldName}-error`);
          if (errorElement) {
            errorElement.textContent = '';
            errorElement.classList.add('hidden');
            field.classList.remove('border-red-500');
          }
        }
      });

      // Clear error when user starts typing
      field.addEventListener('input', () => {
        const fieldName = field.getAttribute('name');
        if (!fieldName) return;

        const errorElement = document.getElementById(`${fieldName}-error`);
        if (errorElement) {
          errorElement.textContent = '';
          errorElement.classList.add('hidden');
          field.classList.remove('border-red-500');
        }
      });
    });

    if (form && formStatus && successMessage && errorMessage) {
      // Prevent the form from submitting normally when the submit button is clicked
      form.addEventListener('submit', async (e) => {
        // This is crucial - prevent the default form submission
        e.preventDefault();

        // Clear previous errors
        clearErrors();

        // Validate all required fields
        let hasErrors = false;

        // Validate name
        const nameField = document.getElementById('name') as HTMLInputElement;
        if (!nameField.value.trim()) {
          showError('name', 'Name is required');
          hasErrors = true;
        }

        // Validate email
        const emailField = document.getElementById('email') as HTMLInputElement;
        if (!emailField.value.trim()) {
          showError('email', 'Email is required');
          hasErrors = true;
        } else if (!isValidEmail(emailField.value.trim())) {
          showError('email', 'Please enter a valid email address');
          hasErrors = true;
        }

        // Validate subject
        const subjectField = document.getElementById('subject') as HTMLInputElement;
        if (!subjectField.value.trim()) {
          showError('subject', 'Subject is required');
          hasErrors = true;
        }

        // Validate message
        const messageField = document.getElementById('message') as HTMLTextAreaElement;
        if (!messageField.value.trim()) {
          showError('message', 'Message is required');
          hasErrors = true;
        }

        // If there are validation errors, stop form submission
        if (hasErrors) {
          return;
        }

        // Hide any previous status messages
        formStatus.classList.remove('hidden');
        successMessage.classList.add('hidden');
        errorMessage.classList.add('hidden');

        try {
          const formData = new FormData(form);

          const response = await fetch('/api/contact', {
            method: 'POST',
            body: formData,
          });

          const result = await response.json();

          if (response.ok) {
            // Show success message
            successMessage.textContent = result.message || 'Your message has been sent successfully!';
            successMessage.classList.remove('hidden');

            // Reset the form
            form.reset();
          } else {
            // Show error message
            errorMessage.textContent = result.message || 'There was an error sending your message. Please try again.';
            errorMessage.classList.remove('hidden');
          }
        } catch (error) {
          console.error('Error submitting form:', error);

          // Show error message
          errorMessage.textContent = 'There was an error sending your message. Please try again.';
          errorMessage.classList.remove('hidden');
        }
      });
    }
  });
</script>
