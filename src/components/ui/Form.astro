---
import type { Form as Props } from '~/types';
import Button from '~/components/ui/Button.astro';

const { inputs, textarea, disclaimer, button = 'Send', description = '' } = Astro.props;
---

<form action="/api/contact" method="POST" id="contact-form">
  {
    inputs &&
      inputs.map(
        ({ type = 'text', name, autocomplete = 'on', placeholder = '', required = true }) =>
          name && (
            <div class="mb-3">
              <input
                type={type}
                name={name}
                id={name}
                autocomplete={autocomplete}
                placeholder={placeholder}
                required={name !== 'company' && required}
                class="py-1.5 px-2 block w-full text-sm rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-slate-900"
              />
              <div class="text-red-500 text-xs mt-1 hidden" id={`${name}-error`} />
            </div>
          )
      )
  }

  {
    textarea && (
      <div>
        <textarea
          id="textarea"
          name={textarea.name ? textarea.name : 'message'}
          rows={textarea.rows ? textarea.rows : 4}
          placeholder={textarea.placeholder}
          required
          class="py-1.5 px-2 block w-full text-sm rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-slate-900"
        />
        <div class="text-red-500 text-xs mt-1 hidden" id="message-error" />
      </div>
    )
  }

  {
    disclaimer && (
      <div class="mt-3 flex items-start">
        <div class="flex mt-0.5">
          <input
            id="disclaimer"
            name="disclaimer"
            type="checkbox"
            class="cursor-pointer mt-1 py-3 px-4 block w-full text-md rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-slate-900"
          />
        </div>
        <div class="ml-3">
          <label for="disclaimer" class="cursor-pointer select-none text-sm text-gray-600 dark:text-gray-400">
            {disclaimer.label}
          </label>
        </div>
      </div>
    )
  }

  {
    button && (
      <div class="mt-10 grid">
        <Button variant="primary" type="submit">
          {button}
        </Button>
      </div>
    )
  }

  {
    description && (
      <div class="mt-3 text-center">
        <p class="text-sm text-gray-600 dark:text-gray-400">{description}</p>
      </div>
    )
  }

  <div id="form-status" class="mt-4 text-center hidden">
    <p id="success-message" class="text-green-600 dark:text-green-400 hidden">
      Your message has been sent successfully!
    </p>
    <p id="error-message" class="text-red-600 dark:text-red-400 hidden">
      There was an error sending your message. Please try again.
    </p>
  </div>
</form>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('contact-form') as HTMLFormElement;
    const formStatus = document.getElementById('form-status');
    const successMessage = document.getElementById('success-message');
    const errorMessage = document.getElementById('error-message');

    // Function to validate email format
    const isValidEmail = (email) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    };

    // Function to show validation error
    const showError = (fieldName, message) => {
      const errorElement = document.getElementById(`${fieldName}-error`);
      if (errorElement) {
        errorElement.textContent = message;
        errorElement.classList.remove('hidden');

        // Add red border to the input field
        const inputField = document.getElementById(fieldName);
        if (inputField) {
          inputField.classList.add('border-red-500');
        }
      }
    };

    // Function to clear validation errors
    const clearErrors = () => {
      const errorElements = document.querySelectorAll('[id$="-error"]');
      errorElements.forEach((element) => {
        element.textContent = '';
        element.classList.add('hidden');
      });

      // Remove red borders
      const inputFields = form.querySelectorAll('input, textarea');
      inputFields.forEach((field) => {
        field.classList.remove('border-red-500');
      });
    };

    // Add input validation on blur
    const inputFields = form.querySelectorAll('input, textarea');
    inputFields.forEach((field) => {
      field.addEventListener('blur', () => {
        const fieldName = field.getAttribute('name');
        const fieldValue = field.value.trim();

        // Skip validation for company field as it's optional
        if (fieldName === 'company') return;

        if (!fieldValue) {
          showError(fieldName, `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`);
        } else if (fieldName === 'email' && !isValidEmail(fieldValue)) {
          showError('email', 'Please enter a valid email address');
        } else {
          // Clear error if field is valid
          const errorElement = document.getElementById(`${fieldName}-error`);
          if (errorElement) {
            errorElement.textContent = '';
            errorElement.classList.add('hidden');
            field.classList.remove('border-red-500');
          }
        }
      });

      // Clear error when user starts typing
      field.addEventListener('input', () => {
        const fieldName = field.getAttribute('name');
        const errorElement = document.getElementById(`${fieldName}-error`);
        if (errorElement) {
          errorElement.textContent = '';
          errorElement.classList.add('hidden');
          field.classList.remove('border-red-500');
        }
      });
    });

    if (form && formStatus && successMessage && errorMessage) {
      form.addEventListener('submit', async (e) => {
        e.preventDefault();

        // Clear previous errors
        clearErrors();

        // Validate all required fields
        let hasErrors = false;

        // Validate name
        const nameField = document.getElementById('name') as HTMLInputElement;
        if (!nameField.value.trim()) {
          showError('name', 'Name is required');
          hasErrors = true;
        }

        // Validate email
        const emailField = document.getElementById('email') as HTMLInputElement;
        if (!emailField.value.trim()) {
          showError('email', 'Email is required');
          hasErrors = true;
        } else if (!isValidEmail(emailField.value.trim())) {
          showError('email', 'Please enter a valid email address');
          hasErrors = true;
        }

        // Validate subject
        const subjectField = document.getElementById('subject') as HTMLInputElement;
        if (!subjectField.value.trim()) {
          showError('subject', 'Subject is required');
          hasErrors = true;
        }

        // Validate message
        const messageField = document.getElementById('textarea') as HTMLTextAreaElement;
        if (!messageField.value.trim()) {
          showError('message', 'Message is required');
          hasErrors = true;
        }

        // If there are validation errors, stop form submission
        if (hasErrors) {
          return;
        }

        // Hide any previous status messages
        formStatus.classList.remove('hidden');
        successMessage.classList.add('hidden');
        errorMessage.classList.add('hidden');

        try {
          const formData = new FormData(form);

          const response = await fetch(form.action, {
            method: 'POST',
            body: formData,
          });

          const result = await response.json();

          if (response.ok) {
            // Show success message
            successMessage.textContent = result.message || 'Your message has been sent successfully!';
            successMessage.classList.remove('hidden');

            // Reset the form
            form.reset();
          } else {
            // Show error message
            errorMessage.textContent = result.message || 'There was an error sending your message. Please try again.';
            errorMessage.classList.remove('hidden');
          }
        } catch (error) {
          console.error('Error submitting form:', error);

          // Show error message
          errorMessage.textContent = 'There was an error sending your message. Please try again.';
          errorMessage.classList.remove('hidden');
        }
      });
    }
  });
</script>
