---
// Simple loading component
export interface Props {
  message?: string;
}

const { message = "Loading..." } = Astro.props;
---

<div id="simple-loader" class="flex flex-col items-center justify-center py-16">
  <!-- Simple spinner -->
  <div class="w-8 h-8 border-2 border-gray-300 border-t-primary rounded-full animate-spin mb-4"></div>
  
  <!-- Loading message -->
  <p class="text-gray-600">{message}</p>
</div>

<script is:inline>
  // Simple loader management
  function hideLoader() {
    const loader = document.getElementById('simple-loader');
    if (loader) {
      loader.style.opacity = '0';
      setTimeout(() => {
        loader.style.display = 'none';
      }, 300);
    }
  }

  // Hide loader when page is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(hideLoader, 500);
    });
  } else {
    setTimeout(hideLoader, 500);
  }

  // Also hide on window load
  window.addEventListener('load', () => {
    setTimeout(hideLoader, 200);
  });
</script>

<style>
  #simple-loader {
    transition: opacity 0.3s ease;
  }
</style>
