---
export interface Props {
  isLoading?: boolean;
  error?: string | null;
  loadingMessage?: string;
  minDisplayTime?: number;
}

const { 
  isLoading = false,
  error = null,
  loadingMessage = "Loading publications from Sanity...",
  minDisplayTime = 800
} = Astro.props;
---

<!-- Server-side loading state -->
{isLoading && (
  <div id="sanity-loader" class="flex flex-col items-center justify-center py-16 px-6">
    <!-- Loading Spinner -->
    <div class="relative mb-6">
      <div class="w-16 h-16 border-4 border-gray-200 border-t-primary rounded-full animate-spin"></div>
      <div class="absolute inset-0 w-16 h-16 border-4 border-transparent border-t-primary-light rounded-full animate-spin" style="animation-delay: -0.15s;"></div>
    </div>
    
    <!-- Loading Message -->
    <div class="text-center max-w-md">
      <h3 class="text-lg font-semibold text-gray-900 mb-2">Loading Publications</h3>
      <p class="text-muted text-sm mb-4" id="loading-message">{loadingMessage}</p>
      
      <!-- Progress Dots -->
      <div class="flex justify-center space-x-1">
        <div class="w-2 h-2 bg-primary rounded-full animate-pulse" style="animation-delay: 0s;"></div>
        <div class="w-2 h-2 bg-primary rounded-full animate-pulse" style="animation-delay: 0.2s;"></div>
        <div class="w-2 h-2 bg-primary rounded-full animate-pulse" style="animation-delay: 0.4s;"></div>
      </div>
    </div>
    
    <!-- Loading Progress Bar -->
    <div class="w-full max-w-xs mt-6">
      <div class="bg-gray-200 rounded-full h-2 overflow-hidden">
        <div id="progress-bar" class="bg-gradient-to-r from-primary to-primary-light h-full rounded-full transition-all duration-300 ease-out" style="width: 0%;"></div>
      </div>
      <p class="text-xs text-gray-500 mt-2 text-center">Fetching data from Sanity CMS...</p>
    </div>
  </div>
)}

<!-- Error State -->
{error && (
  <div class="flex flex-col items-center justify-center py-16 px-6">
    <div class="text-center max-w-md">
      <!-- Error Icon -->
      <div class="w-16 h-16 mx-auto mb-6 text-red-500">
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" class="w-full h-full">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
      </div>
      
      <h3 class="text-lg font-semibold text-gray-900 mb-2">Loading Failed</h3>
      <p class="text-muted text-sm mb-6">{error}</p>
      
      <!-- Retry Button -->
      <button 
        onclick="window.location.reload()" 
        class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
      >
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        Try Again
      </button>
    </div>
  </div>
)}

<script define:vars={{ minDisplayTime }} is:inline>
  // Client-side loading management
  class SanityLoader {
    constructor(minTime = 800) {
      this.minDisplayTime = minTime;
      this.startTime = Date.now();
      this.progressBar = document.getElementById('progress-bar');
      this.loadingMessage = document.getElementById('loading-message');
      this.loader = document.getElementById('sanity-loader');
      
      this.messages = [
        "Connecting to Sanity CMS...",
        "Fetching publication data...",
        "Processing content...",
        "Almost ready..."
      ];
      
      this.init();
    }
    
    init() {
      if (!this.loader) return;
      
      // Start progress animation
      this.animateProgress();
      
      // Cycle through loading messages
      this.cycleMessages();
      
      // Show loading bar
      if (window.loadingBar) {
        window.loadingBar.show();
      }
    }
    
    animateProgress() {
      if (!this.progressBar) return;
      
      let progress = 0;
      const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 90) progress = 90;
        
        this.progressBar.style.width = `${progress}%`;
        
        if (progress >= 90) {
          clearInterval(interval);
        }
      }, 200);
      
      // Store interval for cleanup
      this.progressInterval = interval;
    }
    
    cycleMessages() {
      if (!this.loadingMessage) return;
      
      let messageIndex = 0;
      const messageInterval = setInterval(() => {
        messageIndex = (messageIndex + 1) % this.messages.length;
        this.loadingMessage.textContent = this.messages[messageIndex];
      }, 1500);
      
      // Store interval for cleanup
      this.messageInterval = messageInterval;
    }
    
    complete() {
      const elapsed = Date.now() - this.startTime;
      const remaining = Math.max(0, this.minDisplayTime - elapsed);
      
      setTimeout(() => {
        // Complete progress bar
        if (this.progressBar) {
          this.progressBar.style.width = '100%';
        }
        
        // Update message
        if (this.loadingMessage) {
          this.loadingMessage.textContent = 'Publications loaded!';
        }
        
        // Hide after short delay
        setTimeout(() => {
          if (this.loader) {
            this.loader.style.opacity = '0';
            this.loader.style.transform = 'translateY(-20px)';
            
            setTimeout(() => {
              this.loader.style.display = 'none';
            }, 300);
          }
          
          // Hide loading bar
          if (window.loadingBar) {
            window.loadingBar.complete();
          }
          
          // Cleanup intervals
          if (this.progressInterval) clearInterval(this.progressInterval);
          if (this.messageInterval) clearInterval(this.messageInterval);
        }, 500);
      }, remaining);
    }
  }
  
  // Initialize loader if element exists
  if (document.getElementById('sanity-loader')) {
    window.sanityLoader = new SanityLoader(minDisplayTime);
    
    // Auto-complete when page is fully loaded
    window.addEventListener('load', () => {
      if (window.sanityLoader) {
        window.sanityLoader.complete();
      }
    });
    
    // Also complete on DOM content loaded (fallback)
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
          if (window.sanityLoader) {
            window.sanityLoader.complete();
          }
        }, 300);
      });
    }
  }
</script>

<style>
  #sanity-loader {
    transition: opacity 0.3s ease, transform 0.3s ease;
  }
  
  /* Custom spinner animation */
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  /* Pulsing dots animation */
  @keyframes pulse {
    0%, 100% { opacity: 0.4; }
    50% { opacity: 1; }
  }
  
  /* Progress bar shimmer effect */
  #progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, 
      transparent 0%, 
      rgba(255, 255, 255, 0.4) 50%, 
      transparent 100%
    );
    animation: shimmer 2s infinite;
  }
  
  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }
</style>
