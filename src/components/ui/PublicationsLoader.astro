---
// Client-side loading component that simulates Sanity data loading
export interface Props {
  posts: any[];
  publicationsEnabled: boolean;
}

const { posts, publicationsEnabled } = Astro.props;
---

<!-- Loading State Container (hidden by default, shown by JavaScript) -->
<div id="publications-loading" class="hidden">
  <div class="flex flex-col items-center justify-center py-16 px-6">
    <!-- Sanity Logo/Icon -->
    <div class="relative mb-6">
      <div class="w-16 h-16 border-4 border-gray-200 border-t-primary rounded-full animate-spin"></div>
      <div class="absolute inset-0 w-16 h-16 border-4 border-transparent border-t-primary-light rounded-full animate-spin" style="animation-delay: -0.15s;"></div>
      <!-- Sanity Icon in center -->
      <div class="absolute inset-0 flex items-center justify-center">
        <svg class="w-6 h-6 text-primary" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>
      </div>
    </div>
    
    <!-- Loading Message -->
    <div class="text-center max-w-md">
      <h3 class="text-lg font-semibold text-gray-900 mb-2">Loading Publications</h3>
      <p class="text-muted text-sm mb-4" id="loading-status">Connecting to Sanity CMS...</p>
      
      <!-- Progress Dots -->
      <div class="flex justify-center space-x-1 mb-6">
        <div class="w-2 h-2 bg-primary rounded-full animate-pulse" style="animation-delay: 0s;"></div>
        <div class="w-2 h-2 bg-primary rounded-full animate-pulse" style="animation-delay: 0.2s;"></div>
        <div class="w-2 h-2 bg-primary rounded-full animate-pulse" style="animation-delay: 0.4s;"></div>
      </div>
    </div>
    
    <!-- Loading Progress Bar -->
    <div class="w-full max-w-xs">
      <div class="bg-gray-200 rounded-full h-2 overflow-hidden relative">
        <div id="sanity-progress" class="bg-gradient-to-r from-primary to-primary-light h-full rounded-full transition-all duration-500 ease-out" style="width: 0%;"></div>
      </div>
      <div class="flex justify-between text-xs text-gray-500 mt-2">
        <span>Fetching data...</span>
        <span id="progress-percent">0%</span>
      </div>
    </div>
    
    <!-- Loading Steps -->
    <div class="mt-6 space-y-2 text-sm text-gray-600">
      <div id="step-1" class="flex items-center opacity-50">
        <div class="w-4 h-4 mr-2 border-2 border-gray-300 rounded-full"></div>
        <span>Connecting to Sanity</span>
      </div>
      <div id="step-2" class="flex items-center opacity-50">
        <div class="w-4 h-4 mr-2 border-2 border-gray-300 rounded-full"></div>
        <span>Fetching publications</span>
      </div>
      <div id="step-3" class="flex items-center opacity-50">
        <div class="w-4 h-4 mr-2 border-2 border-gray-300 rounded-full"></div>
        <span>Processing content</span>
      </div>
      <div id="step-4" class="flex items-center opacity-50">
        <div class="w-4 h-4 mr-2 border-2 border-gray-300 rounded-full"></div>
        <span>Rendering posts</span>
      </div>
    </div>
  </div>
</div>

<!-- Actual Content (hidden during loading) -->
<div id="publications-content" class="hidden">
  {publicationsEnabled && posts.length > 0 && (
    <>
      <div id="list-view">
        <slot name="list-view" />
      </div>
      <div id="grid-view" class="hidden">
        <slot name="grid-view" />
      </div>
    </>
  )}
  
  {publicationsEnabled && posts.length === 0 && (
    <div class="text-center py-12">
      <p class="text-xl text-muted">No publications available at the moment.</p>
      <p class="text-muted mt-2">Check back soon for new insights and research.</p>
    </div>
  )}
</div>

<script define:vars={{ posts, publicationsEnabled }} is:inline>
  class PublicationsLoader {
    constructor() {
      this.loadingEl = document.getElementById('publications-loading');
      this.contentEl = document.getElementById('publications-content');
      this.progressBar = document.getElementById('sanity-progress');
      this.progressPercent = document.getElementById('progress-percent');
      this.statusEl = document.getElementById('loading-status');
      
      this.steps = [
        { id: 'step-1', message: 'Connecting to Sanity CMS...', duration: 800 },
        { id: 'step-2', message: 'Fetching publications data...', duration: 1200 },
        { id: 'step-3', message: 'Processing content...', duration: 600 },
        { id: 'step-4', message: 'Rendering posts...', duration: 400 }
      ];
      
      this.currentStep = 0;
      this.progress = 0;
      
      this.init();
    }
    
    init() {
      // Only show loading if we have publications enabled and are on publications page
      if (!publicationsEnabled || !this.loadingEl) return;
      
      // Show loading state
      this.loadingEl.classList.remove('hidden');
      this.contentEl.classList.add('hidden');
      
      // Show loading bar
      if (window.loadingBar) {
        window.loadingBar.show();
      }
      
      // Start the loading sequence
      this.startLoading();
    }
    
    startLoading() {
      let totalDuration = 0;
      let currentDuration = 0;
      
      // Calculate total duration
      this.steps.forEach(step => totalDuration += step.duration);
      
      // Execute each step
      this.steps.forEach((step, index) => {
        setTimeout(() => {
          this.executeStep(step, index);
          currentDuration += step.duration;
          this.updateProgress((currentDuration / totalDuration) * 100);
        }, currentDuration);
        
        currentDuration += step.duration;
      });
      
      // Complete loading
      setTimeout(() => {
        this.completeLoading();
      }, totalDuration + 200);
    }
    
    executeStep(step, index) {
      // Update status message
      if (this.statusEl) {
        this.statusEl.textContent = step.message;
      }
      
      // Mark current step as active
      const stepEl = document.getElementById(step.id);
      if (stepEl) {
        stepEl.classList.remove('opacity-50');
        stepEl.classList.add('opacity-100');
        
        // Update step indicator
        const indicator = stepEl.querySelector('div');
        if (indicator) {
          indicator.classList.remove('border-gray-300');
          indicator.classList.add('border-primary', 'bg-primary');
        }
      }
      
      // Add checkmark to previous steps
      if (index > 0) {
        const prevStepEl = document.getElementById(this.steps[index - 1].id);
        if (prevStepEl) {
          const indicator = prevStepEl.querySelector('div');
          if (indicator) {
            indicator.innerHTML = '<svg class="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>';
          }
        }
      }
    }
    
    updateProgress(percent) {
      this.progress = Math.min(percent, 100);
      
      if (this.progressBar) {
        this.progressBar.style.width = `${this.progress}%`;
      }
      
      if (this.progressPercent) {
        this.progressPercent.textContent = `${Math.round(this.progress)}%`;
      }
    }
    
    completeLoading() {
      // Complete progress
      this.updateProgress(100);
      
      // Update final status
      if (this.statusEl) {
        this.statusEl.textContent = `Loaded ${posts.length} publications successfully!`;
      }
      
      // Mark final step as complete
      const finalStep = document.getElementById('step-4');
      if (finalStep) {
        const indicator = finalStep.querySelector('div');
        if (indicator) {
          indicator.innerHTML = '<svg class="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>';
        }
      }
      
      // Hide loading and show content after a brief delay
      setTimeout(() => {
        if (this.loadingEl) {
          this.loadingEl.style.opacity = '0';
          this.loadingEl.style.transform = 'translateY(-20px)';
        }
        
        setTimeout(() => {
          if (this.loadingEl) this.loadingEl.classList.add('hidden');
          if (this.contentEl) this.contentEl.classList.remove('hidden');
          
          // Hide loading bar
          if (window.loadingBar) {
            window.loadingBar.complete();
          }
          
          // Fade in content
          if (this.contentEl) {
            this.contentEl.style.opacity = '0';
            this.contentEl.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
              this.contentEl.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
              this.contentEl.style.opacity = '1';
              this.contentEl.style.transform = 'translateY(0)';
            }, 50);
          }
        }, 300);
      }, 500);
    }
  }
  
  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      window.publicationsLoader = new PublicationsLoader();
    });
  } else {
    window.publicationsLoader = new PublicationsLoader();
  }
</script>

<style>
  #publications-loading {
    transition: opacity 0.3s ease, transform 0.3s ease;
  }
  
  #publications-content {
    transition: opacity 0.5s ease, transform 0.5s ease;
  }
  
  /* Step indicators */
  #publications-loading .flex.items-center {
    transition: opacity 0.3s ease;
  }
  
  /* Progress bar shimmer */
  #sanity-progress::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, 
      transparent 0%, 
      rgba(255, 255, 255, 0.4) 50%, 
      transparent 100%
    );
    animation: shimmer 2s infinite;
  }
  
  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }
</style>
