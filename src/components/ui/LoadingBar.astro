---
export interface Props {
  id?: string;
  class?: string;
  duration?: number;
}

const { id = 'loading-bar', class: className = '', duration = 2000 } = Astro.props;
---

<div
  id={id}
  class={`fixed top-0 left-0 w-full h-1 bg-gradient-to-r from-primary via-primary-light to-primary z-50 transform -translate-x-full transition-transform duration-300 ease-out ${className}`}
  style="display: none; top: var(--header-height, 0px);"
>
  <div class="h-full bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse"></div>
</div>

<script define:vars={{ id, duration }} is:inline>
  class LoadingBar {
    constructor(elementId, animationDuration = 2000) {
      this.element = document.getElementById(elementId);
      this.duration = animationDuration;
      this.isVisible = false;
    }

    show() {
      if (!this.element || this.isVisible) return;

      this.isVisible = true;
      this.element.style.display = 'block';

      // Start the loading animation
      requestAnimationFrame(() => {
        this.element.style.transform = 'translateX(0%)';

        // Animate the progress
        setTimeout(() => {
          this.element.style.transform = 'translateX(100%)';
        }, 100);
      });
    }

    hide() {
      if (!this.element || !this.isVisible) return;

      this.isVisible = false;
      this.element.style.transform = 'translateX(100%)';

      setTimeout(() => {
        this.element.style.display = 'none';
        this.element.style.transform = 'translateX(-100%)';
      }, 300);
    }

    complete() {
      if (!this.element) return;

      this.element.style.transform = 'translateX(100%)';
      setTimeout(() => this.hide(), 200);
    }
  }

  // Create global loading bar instance
  window.loadingBar = new LoadingBar(id, duration);

  // Auto-hide after duration if still visible
  setTimeout(() => {
    if (window.loadingBar && window.loadingBar.isVisible) {
      window.loadingBar.complete();
    }
  }, duration);
</script>

<style>
  #loading-bar {
    background: linear-gradient(
      90deg,
      var(--aw-color-primary) 0%,
      var(--aw-color-primary-light, #60a5fa) 50%,
      var(--aw-color-primary) 100%
    );
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
  }

  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  #loading-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
    animation: shimmer 1.5s infinite;
  }
</style>
