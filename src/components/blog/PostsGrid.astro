---
import type { Post } from '~/types';
import { Icon } from 'astro-icon/components';
import Image from '~/components/common/Image.astro';
import PostTags from '~/components/blog/Tags.astro';
import { APP_BLOG } from 'gameometry:config';
import { getPermalink } from '~/utils/permalinks';
import { findImage } from '~/utils/images';
import { getFormattedDate } from '~/utils/utils';

export interface Props {
  posts: Array<Post>;
}

const { posts } = Astro.props;
---

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  {
    posts.map(async (post) => {
      const image = await findImage(post.image);
      const link = APP_BLOG?.post?.isEnabled ? getPermalink(post.permalink, 'post') : '';
      
      return (
        <article class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 flex flex-col h-full">
          {image && (
            <div class="relative">
              <Image
                src={image}
                class="w-full h-48 object-cover"
                widths={[400, 900]}
                width={400}
                sizes="(max-width: 900px) 400px, 900px"
                alt={post.title}
                aspectRatio="16:9"
                loading="lazy"
                decoding="async"
              />
              {post.category && (
                <div class="absolute top-4 left-4">
                  <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">
                    {post.category.title}
                  </span>
                </div>
              )}
            </div>
          )}
          
          <div class="p-6 flex flex-col flex-grow">
            <div class="flex items-center text-sm text-muted mb-3">
              <Icon name="tabler:calendar" class="w-4 h-4 mr-2" />
              <time datetime={String(post.publishDate)}>
                {getFormattedDate(post.publishDate)}
              </time>
              
              {post.readingTime && (
                <>
                  <span class="mx-2">•</span>
                  <Icon name="tabler:clock" class="w-4 h-4 mr-1" />
                  <span>{post.readingTime} min read</span>
                </>
              )}
            </div>
            
            <h2 class="text-xl font-bold leading-tight mb-3 flex-grow">
              {link ? (
                <a
                  class="hover:text-primary transition ease-in duration-200"
                  href={link}
                >
                  {post.title}
                </a>
              ) : (
                post.title
              )}
            </h2>
            
            {post.excerpt && (
              <p class="text-muted mb-4 line-clamp-3 flex-grow">
                {post.excerpt}
              </p>
            )}
            
            <div class="mt-auto">
              {post.author && (
                <div class="flex items-center text-sm text-muted mb-3">
                  <Icon name="tabler:user" class="w-4 h-4 mr-2" />
                  <span>{post.author}</span>
                </div>
              )}
              
              {post.tags && post.tags.length > 0 && (
                <div class="mb-4">
                  <PostTags tags={post.tags} class="text-sm" />
                </div>
              )}
              
              {link && (
                <a
                  class="inline-flex items-center text-primary hover:text-primary-dark font-medium"
                  href={link}
                >
                  Read more
                  <Icon name="tabler:arrow-right" class="w-4 h-4 ml-1" />
                </a>
              )}
            </div>
          </div>
        </article>
      );
    })
  }
</div>
