---
import type { Post } from '~/types';
import { Icon } from 'astro-icon/components';
import Image from '~/components/common/Image.astro';
import PostTags from '~/components/blog/Tags.astro';
import { APP_BLOG } from 'gameometry:config';
import { getPermalink } from '~/utils/permalinks';
import { findImage } from '~/utils/images';
import { getFormattedDate } from '~/utils/utils';

export interface Props {
  posts: Array<Post>;
}

const { posts } = Astro.props;
---

<div class="space-y-8">
  {
    posts.map(async (post) => {
      const image = await findImage(post.image);
      const link = APP_BLOG?.post?.isEnabled ? getPermalink(post.permalink, 'post') : '';
      
      return (
        <article class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
          <div class="md:flex">
            {image && (
              <div class="md:w-1/3">
                <Image
                  src={image}
                  class="w-full h-48 md:h-full object-cover"
                  widths={[400, 900]}
                  width={400}
                  sizes="(max-width: 900px) 400px, 900px"
                  alt={post.title}
                  aspectRatio="16:9"
                  loading="lazy"
                  decoding="async"
                />
              </div>
            )}
            
            <div class={`p-6 ${image ? 'md:w-2/3' : 'w-full'}`}>
              <div class="flex items-center text-sm text-muted mb-3">
                <Icon name="tabler:calendar" class="w-4 h-4 mr-2" />
                <time datetime={String(post.publishDate)} class="mr-4">
                  {getFormattedDate(post.publishDate)}
                </time>
                
                {post.category && (
                  <>
                    <Icon name="tabler:folder" class="w-4 h-4 mr-2" />
                    <span class="capitalize">{post.category.title}</span>
                  </>
                )}
              </div>
              
              <h2 class="text-xl md:text-2xl font-bold leading-tight mb-3">
                {link ? (
                  <a
                    class="hover:text-primary transition ease-in duration-200"
                    href={link}
                  >
                    {post.title}
                  </a>
                ) : (
                  post.title
                )}
              </h2>
              
              {post.excerpt && (
                <p class="text-muted mb-4 line-clamp-3">
                  {post.excerpt}
                </p>
              )}
              
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  {post.author && (
                    <div class="flex items-center text-sm text-muted">
                      <Icon name="tabler:user" class="w-4 h-4 mr-2" />
                      <span>{post.author}</span>
                    </div>
                  )}
                  
                  {post.readingTime && (
                    <div class="flex items-center text-sm text-muted ml-4">
                      <Icon name="tabler:clock" class="w-4 h-4 mr-2" />
                      <span>{post.readingTime} min read</span>
                    </div>
                  )}
                </div>
                
                {link && (
                  <a
                    class="inline-flex items-center text-primary hover:text-primary-dark font-medium"
                    href={link}
                  >
                    Read more
                    <Icon name="tabler:arrow-right" class="w-4 h-4 ml-1" />
                  </a>
                )}
              </div>
              
              {post.tags && post.tags.length > 0 && (
                <div class="mt-4 pt-4 border-t border-gray-200">
                  <PostTags tags={post.tags} class="text-sm" />
                </div>
              )}
            </div>
          </div>
        </article>
      );
    })
  }
</div>
