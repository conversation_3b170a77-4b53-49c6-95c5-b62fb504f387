import { getPermalink, getHomePermalink, getBlogPermalink } from './utils/permalinks';
import favicon from 'src/assets/favicons/favicon.ico';
import { isPublicationsEnabled } from './utils/feature-flags';

// Build navigation links conditionally based on feature flags
const buildNavigationLinks = () => {
  const links = [
    {
      text: 'Home',
      href: getHomePermalink(),
      /*links: [
        {
          text: 'Privacy policy',
          href: getPermalink('/privacy'),
        },
      ], */
    },
    {
      text: 'About us',
      href: getPermalink('/about'),
    },
    {
      text: 'Services',
      href: getPermalink('/services'),
    },
  ];

  // Conditionally add Publications link
  if (isPublicationsEnabled()) {
    links.push({
      text: 'Publications',
      href: getPermalink('/publications'),
    });
  }

  // Always add Contact link
  links.push({
    text: 'Contact',
    href: getPermalink('/contact'),
  });

  return links;
};

export const headerData = {
  links: buildNavigationLinks(),
};

export const footerData = {
  links: [],
  secondaryLinks: [],
  socialLinks: [],
  footNote: `
   <img class="w-5 h-5 md:w-6 md:h-6 md:-mt-0.5 bg-cover mr-1.5 rtl:mr-0 rtl:ml-1.5 float-left rtl:float-right rounded-sm" src="${favicon}" alt="gameometry logo" loading="lazy"/>
    Made by <a class="text-blue-600 underline dark:text-muted" href="https://gameometry.io/"> gameometry</a> · All rights reserved 2025.
  `,
};
