import path from 'path';
import { fileURLToPath } from 'url';
import { loadEnv } from 'vite';

import { defineConfig } from 'astro/config';

import sitemap from '@astrojs/sitemap';
import tailwind from '@astrojs/tailwind';
import mdx from '@astrojs/mdx';
import partytown from '@astrojs/partytown';
import icon from 'astro-icon';
import compress from 'astro-compress';
import vercel from '@astrojs/vercel';
import type { AstroIntegration } from 'astro';

import gameometry from './vendor/integration';

import { readingTimeRemarkPlugin, responsiveTablesRehypePlugin, lazyImagesRehypePlugin } from './src/utils/frontmatter';

import sanity from '@sanity/astro';
import react from '@astrojs/react';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Load environment variables using Vite's loadEnv
const env = loadEnv('', process.cwd(), '');

// Load environment variables
const SANITY_PROJECT_ID = env.PUBLIC_SANITY_PROJECT_ID;
const SANITY_DATASET = env.PUBLIC_SANITY_DATASET || 'production';

// Validate required environment variables
if (!SANITY_PROJECT_ID) {
  throw new Error('PUBLIC_SANITY_PROJECT_ID environment variable is required. Please check your .env file.');
}

const hasExternalScripts = false;
const whenExternalScripts = (items: (() => AstroIntegration) | (() => AstroIntegration)[] = []) =>
  hasExternalScripts ? (Array.isArray(items) ? items.map((item) => item()) : [items()]) : [];

export default defineConfig({
  output: 'server',
  adapter: vercel({
    webAnalytics: {
      enabled: true,
    },
  }),

  integrations: [
    sanity({
      projectId: SANITY_PROJECT_ID,
      dataset: SANITY_DATASET,
      useCdn: false, // See note on using the CDN
      apiVersion: "2024-07-24", // insert the current date to access the latest version of the API
      studioBasePath: "/studio", // If you want to access the Studio on a route
      stega: {
        studioUrl: "/studio",
      },
    }),
    tailwind({
      applyBaseStyles: false,
    }), sitemap(), mdx(), icon({
      include: {
        tabler: ['*'],
        'flat-color-icons': [
          'template',
          'gallery',
          'approval',
          'document',
          'advertising',
          'currency-exchange',
          'voice-presentation',
          'business-contact',
          'database',
        ],
      },
    }), ...whenExternalScripts(() =>
      partytown({
        config: { forward: ['dataLayer.push'] },
      })
    ), compress({
      CSS: true,
      HTML: {
        'html-minifier-terser': {
          removeAttributeQuotes: false,
        },
      },
      Image: false,
      JavaScript: true,
      SVG: false,
      Logger: 1,
    }), gameometry({
      config: './src/config.yaml',
    }), react()],

  image: {
    domains: ['cdn.pixabay.com'],
  },

  markdown: {
    remarkPlugins: [readingTimeRemarkPlugin],
    rehypePlugins: [responsiveTablesRehypePlugin, lazyImagesRehypePlugin],
  },

  vite: {
    resolve: {
      alias: {
        '~': path.resolve(__dirname, './src'),
      },
    },
  },
});
