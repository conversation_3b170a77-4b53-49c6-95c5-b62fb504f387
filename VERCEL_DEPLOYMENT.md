# Deploying to Vercel

This guide explains how to deploy your Astro application with server-side rendering to Vercel.

## Prerequisites

- A [Vercel](https://vercel.com) account
- [Git](https://git-scm.com/) installed on your local machine
- Your project pushed to a Git repository (GitHub, GitLab, or Bitbucket)

## Deployment Steps

### 1. Connect Your Repository to Vercel

1. Log in to your Vercel account
2. Click "Add New" > "Project"
3. Import your Git repository
4. Select the repository containing your Astro project

### 2. Configure Project Settings

Vercel will automatically detect that you're using Astro. The default settings should work, but you can verify:

- **Framework Preset**: Astro
- **Build Command**: `npm run build`
- **Output Directory**: `dist`
- **Install Command**: `npm install`

### 3. Environment Variables

Add the following environment variables in the Vercel project settings:

- `BREVO_API_KEY`: Your Brevo API key for sending emails

To add environment variables:
1. Go to your project settings
2. Navigate to the "Environment Variables" tab
3. Add each variable with its corresponding value
4. Save your changes

### 4. Deploy

Click "Deploy" and Vercel will build and deploy your application.

## Verifying Your Deployment

After deployment:

1. Visit your deployed site
2. Navigate to the contact page
3. Fill out the contact form and submit it
4. Verify that the form submission works and emails are <NAME_EMAIL>

## Troubleshooting

If you encounter issues:

1. Check the Vercel deployment logs for errors
2. Verify that your environment variables are correctly set
3. Make sure your Brevo API key is valid and has permissions to send emails
4. Check that the email addresses (sender and recipient) are properly configured

## Updating Your Deployment

Any new commits pushed to your main branch will automatically trigger a new deployment on Vercel.

For manual deployments:
1. Go to your Vercel dashboard
2. Select your project
3. Click "Deployments"
4. Click "Deploy" to manually trigger a new deployment
